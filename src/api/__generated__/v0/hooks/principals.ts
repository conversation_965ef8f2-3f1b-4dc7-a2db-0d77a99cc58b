/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v0/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  Error,
  GetP<PERSON><PERSON>palsP<PERSON><PERSON>,
  Principal,
  PrincipalResponse,
  PrincipalsResponse,
} from '@floqastinc/transform-v0';
import {
  queryOptions,
  type UndefinedInitialDataOptions,
  useQuery,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { usePrincipalService } from './context';
import { assert, compact, guard, type QueryError } from './runtime';

const useGetCurrentPrincipalQueryOptions = () => {
  const principalService = usePrincipalService();
  return queryOptions<PrincipalResponse, QueryError<Error[]>, Principal>({
    queryKey: [`/principals/me`],
    queryFn: async () => {
      const res = await guard(principalService.getCurrentPrincipal());
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get the current principal
 */
export function useCurrentPrincipal(
  options?: Omit<
    UndefinedInitialDataOptions<PrincipalResponse, QueryError<Error[]>, Principal>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetCurrentPrincipalQueryOptions();
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get the current principal
 */
export function useSuspenseCurrentPrincipal(
  options?: Omit<
    UndefinedInitialDataOptions<PrincipalResponse, QueryError<Error[]>, Principal>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetCurrentPrincipalQueryOptions();
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetPrincipalsQueryOptions = (params: GetPrincipalsParams) => {
  const principalService = usePrincipalService();
  return queryOptions<PrincipalsResponse, QueryError<Error[]>, Principal[]>({
    queryKey: [`/principals`, compact({ ids: params.ids.join(',') })].filter(Boolean),
    queryFn: async () => {
      const res = await guard(principalService.getPrincipals(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * Get the principal
 */
export function usePrincipals(
  params: GetPrincipalsParams,
  options?: Omit<
    UndefinedInitialDataOptions<PrincipalsResponse, QueryError<Error[]>, Principal[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetPrincipalsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get the principal
 */
export function useSuspensePrincipals(
  params: GetPrincipalsParams,
  options?: Omit<
    UndefinedInitialDataOptions<PrincipalsResponse, QueryError<Error[]>, Principal[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetPrincipalsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
