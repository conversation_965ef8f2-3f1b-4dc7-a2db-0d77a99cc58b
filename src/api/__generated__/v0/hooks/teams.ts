/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v0/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  CreateTeamParams,
  DeleteTeamParams,
  Error,
  GetTeamParams,
  GetTeamsParams,
  Team,
  TeamResponse,
  TeamsResponse,
  UpdateTeamParams,
} from '@floqastinc/transform-v0';
import {
  type InfiniteData,
  queryOptions,
  type UndefinedInitialDataOptions,
  useInfiniteQuery,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseInfiniteQuery,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { useTeamService } from './context';
import {
  applyPageParam,
  assert,
  compact,
  getInitialPageParam,
  getNextPageParam,
  getPreviousPageParam,
  guard,
  type PageParam,
  type QueryError,
} from './runtime';

/**
 * Create a new team
 */
export function useCreateTeam(
  options?: Omit<UseMutationOptions<Team, QueryError<Error[]>, CreateTeamParams>, 'mutationFn'>,
) {
  const queryClient = useQueryClient();
  const teamService = useTeamService();
  return useMutation({
    mutationFn: async (params: CreateTeamParams) => {
      const res = await guard(teamService.createTeam(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/teams`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Delete a team
 */
export function useDeleteTeam(
  options?: Omit<UseMutationOptions<Team, QueryError<Error[]>, DeleteTeamParams>, 'mutationFn'>,
) {
  const queryClient = useQueryClient();
  const teamService = useTeamService();
  return useMutation({
    mutationFn: async (params: DeleteTeamParams) => {
      const res = await guard(teamService.deleteTeam(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/teams/${params.teamId}`] });
      queryClient.invalidateQueries({ queryKey: [`/teams`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetTeamQueryOptions = (params: GetTeamParams) => {
  const teamService = useTeamService();
  return queryOptions<TeamResponse, QueryError<Error[]>, Team>({
    queryKey: [`/teams/${params.teamId}`],
    queryFn: async () => {
      const res = await guard(teamService.getTeam(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get a team
 */
export function useTeam(
  params: GetTeamParams,
  options?: Omit<
    UndefinedInitialDataOptions<TeamResponse, QueryError<Error[]>, Team>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTeamQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get a team
 */
export function useSuspenseTeam(
  params: GetTeamParams,
  options?: Omit<
    UndefinedInitialDataOptions<TeamResponse, QueryError<Error[]>, Team>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTeamQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTeamsQueryOptions = (params?: GetTeamsParams) => {
  const teamService = useTeamService();
  return queryOptions<TeamsResponse, QueryError<Error[]>>({
    queryKey: [
      `/teams`,
      compact({
        externalId: params?.externalId,
        first: params?.first,
        after: params?.after,
        last: params?.last,
        before: params?.before,
      }),
    ].filter(Boolean),
    queryFn: async () => {
      const res = await guard(teamService.getTeams(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
  });
};

/**
 * List all teams
 */
export function useTeams(
  params?: GetTeamsParams,
  options?: Omit<
    UndefinedInitialDataOptions<TeamsResponse, QueryError<Error[]>>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTeamsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all teams
 */
export function useSuspenseTeams(
  params?: GetTeamsParams,
  options?: Omit<
    UndefinedInitialDataOptions<TeamsResponse, QueryError<Error[]>>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTeamsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
function useInfiniteTeamsQueryOptions(params?: GetTeamsParams) {
  const teamService = useTeamService();
  return {
    queryKey: [`/teams`, compact({ externalId: params?.externalId }), { inifinite: true }].filter(
      Boolean,
    ),
    queryFn: async ({ pageParam }: PageParam) => {
      const res = await guard(teamService.getTeams(applyPageParam(params ?? {}, pageParam)));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data: InfiniteData<TeamsResponse, string | undefined>) =>
      data.pages.flatMap((page) => page.data ?? []),
    initialPageParam: getInitialPageParam(params ?? {}),
    getNextPageParam,
    getPreviousPageParam,
  };
}

/**
 * List all teams
 */
export const useInfiniteTeams = (params?: GetTeamsParams) => {
  const options = useInfiniteTeamsQueryOptions(params);
  return useInfiniteQuery(options);
};

/**
 * List all teams
 */
export const useSuspenseInfiniteTeams = (params?: GetTeamsParams) => {
  const options = useInfiniteTeamsQueryOptions(params);
  return useSuspenseInfiniteQuery(options);
};

/**
 * Update a team
 */
export function useUpdateTeam(
  options?: Omit<UseMutationOptions<Team, QueryError<Error[]>, UpdateTeamParams>, 'mutationFn'>,
) {
  const queryClient = useQueryClient();
  const teamService = useTeamService();
  return useMutation({
    mutationFn: async (params: UpdateTeamParams) => {
      const res = await guard(teamService.updateTeam(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/teams/${params.teamId}`] });
      queryClient.invalidateQueries({ queryKey: [`/teams`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}
