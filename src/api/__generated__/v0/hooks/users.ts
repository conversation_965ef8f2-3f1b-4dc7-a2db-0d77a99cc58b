/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v0/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  CreateUserParams,
  DeleteUserParams,
  Error,
  GetUserParams,
  GetUsersByTeamParams,
  GetUsersParams,
  ResendInviteParams,
  ResetPasswordParams,
  UpdateUserParams,
  User,
  UserResponse,
  UsersResponse,
} from '@floqastinc/transform-v0';
import {
  type InfiniteData,
  queryOptions,
  type UndefinedInitialDataOptions,
  useInfiniteQuery,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseInfiniteQuery,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { useUserService } from './context';
import {
  applyPageParam,
  assert,
  compact,
  getInitialPageParam,
  getNextPageParam,
  getPreviousPageParam,
  guard,
  type PageParam,
  type QueryError,
} from './runtime';

/**
 * Create a new user
 */
export function useCreateUser(
  options?: Omit<UseMutationOptions<User, QueryError<Error[]>, CreateUserParams>, 'mutationFn'>,
) {
  const queryClient = useQueryClient();
  const userService = useUserService();
  return useMutation({
    mutationFn: async (params: CreateUserParams) => {
      const res = await guard(userService.createUser(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/users`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetCurrentUserQueryOptions = () => {
  const userService = useUserService();
  return queryOptions<UserResponse, QueryError<Error[]>, User>({
    queryKey: [`/users/me`],
    queryFn: async () => {
      const res = await guard(userService.getCurrentUser());
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get the current user
 */
export function useCurrentUser(
  options?: Omit<
    UndefinedInitialDataOptions<UserResponse, QueryError<Error[]>, User>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetCurrentUserQueryOptions();
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get the current user
 */
export function useSuspenseCurrentUser(
  options?: Omit<
    UndefinedInitialDataOptions<UserResponse, QueryError<Error[]>, User>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetCurrentUserQueryOptions();
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

/**
 * Delete a user
 */
export function useDeleteUser(
  options?: Omit<UseMutationOptions<User, QueryError<Error[]>, DeleteUserParams>, 'mutationFn'>,
) {
  const queryClient = useQueryClient();
  const userService = useUserService();
  return useMutation({
    mutationFn: async (params: DeleteUserParams) => {
      const res = await guard(userService.deleteUser(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/users/${params.userId}`] });
      queryClient.invalidateQueries({ queryKey: [`/users`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Resend the invitation email for a user
 */
export function useResendInvite(
  options?: Omit<UseMutationOptions<User, QueryError<Error[]>, ResendInviteParams>, 'mutationFn'>,
) {
  const queryClient = useQueryClient();
  const userService = useUserService();
  return useMutation({
    mutationFn: async (params: ResendInviteParams) => {
      const res = await guard(userService.resendInvite(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/users/${params.userId}/resendInvite`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Initiate a password reset for a user
 */
export function useResetPassword(
  options?: Omit<UseMutationOptions<User, QueryError<Error[]>, ResetPasswordParams>, 'mutationFn'>,
) {
  const queryClient = useQueryClient();
  const userService = useUserService();
  return useMutation({
    mutationFn: async (params: ResetPasswordParams) => {
      const res = await guard(userService.resetPassword(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/users/${params.userId}/resetPassword`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Update a user
 */
export function useUpdateUser(
  options?: Omit<UseMutationOptions<User, QueryError<Error[]>, UpdateUserParams>, 'mutationFn'>,
) {
  const queryClient = useQueryClient();
  const userService = useUserService();
  return useMutation({
    mutationFn: async (params: UpdateUserParams) => {
      const res = await guard(userService.updateUser(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/users/${params.userId}`] });
      queryClient.invalidateQueries({ queryKey: [`/users`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetUserQueryOptions = (params: GetUserParams) => {
  const userService = useUserService();
  return queryOptions<UserResponse, QueryError<Error[]>, User>({
    queryKey: [`/users/${params.userId}`],
    queryFn: async () => {
      const res = await guard(userService.getUser(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get a user
 */
export function useUser(
  params: GetUserParams,
  options?: Omit<
    UndefinedInitialDataOptions<UserResponse, QueryError<Error[]>, User>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetUserQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get a user
 */
export function useSuspenseUser(
  params: GetUserParams,
  options?: Omit<
    UndefinedInitialDataOptions<UserResponse, QueryError<Error[]>, User>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetUserQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetUsersQueryOptions = (params?: GetUsersParams) => {
  const userService = useUserService();
  return queryOptions<UsersResponse, QueryError<Error[]>>({
    queryKey: [
      `/users`,
      compact({
        first: params?.first,
        after: params?.after,
        last: params?.last,
        before: params?.before,
      }),
    ].filter(Boolean),
    queryFn: async () => {
      const res = await guard(userService.getUsers(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
  });
};

/**
 * List all users
 */
export function useUsers(
  params?: GetUsersParams,
  options?: Omit<
    UndefinedInitialDataOptions<UsersResponse, QueryError<Error[]>>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetUsersQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all users
 */
export function useSuspenseUsers(
  params?: GetUsersParams,
  options?: Omit<
    UndefinedInitialDataOptions<UsersResponse, QueryError<Error[]>>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetUsersQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
function useInfiniteUsersQueryOptions(params?: GetUsersParams) {
  const userService = useUserService();
  return {
    queryKey: [`/users`, { inifinite: true }],
    queryFn: async ({ pageParam }: PageParam) => {
      const res = await guard(userService.getUsers(applyPageParam(params ?? {}, pageParam)));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data: InfiniteData<UsersResponse, string | undefined>) =>
      data.pages.flatMap((page) => page.data ?? []),
    initialPageParam: getInitialPageParam(params ?? {}),
    getNextPageParam,
    getPreviousPageParam,
  };
}

/**
 * List all users
 */
export const useInfiniteUsers = (params?: GetUsersParams) => {
  const options = useInfiniteUsersQueryOptions(params);
  return useInfiniteQuery(options);
};

/**
 * List all users
 */
export const useSuspenseInfiniteUsers = (params?: GetUsersParams) => {
  const options = useInfiniteUsersQueryOptions(params);
  return useSuspenseInfiniteQuery(options);
};

const useGetUsersByTeamQueryOptions = (params: GetUsersByTeamParams) => {
  const userService = useUserService();
  return queryOptions<UsersResponse, QueryError<Error[]>>({
    queryKey: [
      `/teams/${params.teamId}/users`,
      compact({
        first: params.first,
        after: params.after,
        last: params.last,
        before: params.before,
      }),
    ].filter(Boolean),
    queryFn: async () => {
      const res = await guard(userService.getUsersByTeam(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
  });
};

/**
 * Get users for a team
 */
export function useUsersByTeam(
  params: GetUsersByTeamParams,
  options?: Omit<
    UndefinedInitialDataOptions<UsersResponse, QueryError<Error[]>>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetUsersByTeamQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get users for a team
 */
export function useSuspenseUsersByTeam(
  params: GetUsersByTeamParams,
  options?: Omit<
    UndefinedInitialDataOptions<UsersResponse, QueryError<Error[]>>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetUsersByTeamQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
function useInfiniteUsersByTeamQueryOptions(params: GetUsersByTeamParams) {
  const userService = useUserService();
  return {
    queryKey: [`/teams/${params.teamId}/users`, { inifinite: true }],
    queryFn: async ({ pageParam }: PageParam) => {
      const res = await guard(userService.getUsersByTeam(applyPageParam(params, pageParam)));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data: InfiniteData<UsersResponse, string | undefined>) =>
      data.pages.flatMap((page) => page.data ?? []),
    initialPageParam: getInitialPageParam(params),
    getNextPageParam,
    getPreviousPageParam,
  };
}

/**
 * Get users for a team
 */
export const useInfiniteUsersByTeam = (params: GetUsersByTeamParams) => {
  const options = useInfiniteUsersByTeamQueryOptions(params);
  return useInfiniteQuery(options);
};

/**
 * Get users for a team
 */
export const useSuspenseInfiniteUsersByTeam = (params: GetUsersByTeamParams) => {
  const options = useInfiniteUsersByTeamQueryOptions(params);
  return useSuspenseInfiniteQuery(options);
};
