<!--
This code was generated by @basketry/react-query@0.1.0-alpha.9

Changes to this file may cause incorrect behavior and will be lost if
the code is regenerated.

To make changes to the contents of this file:
1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
2. Run `npx basketry`

About Basketry: https://basketry.io
About @basketry/react-query: https://github.com/basketry/react-query#readme
-->

# React Query Hooks

This directory contains the generated React Query hooks that provide access to the Transform v3 API.

For more information about React Query, [read the official docs](https://tanstack.com/query/latest/docs/framework/react/overview).

## Setup

Wrap your application in the `TransformProvider` exported from the `context` module. This provides implementations of the interfaces that empower the query and mutation hooks.

```tsx
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { TransformProvider } from './hooks/context';

export const App = () => {
  const queryClient = new QueryClient();

  return (
    <TransformProvider root="/v3">
      <QueryClientProvider client={queryClient}>
        <div>Your app goes here</div>
      </QueryClientProvider>
    </TransformProvider>
  );
};
```

Note that the `TransformProvider` _DOES NOT_ automatically service as a Tanstack `QueryClientProvider`. You will also need to wrap your component tree in a `QueryClientProvider`. It doesn't matter which order you wrap the components, but both are required.

## Queries

See: [Tanstack Query docs for Queries](https://tanstack.com/query/latest/docs/framework/react/guides/queries)

Each query hook is the equivalent of the general `useQuery` hook with the method-specific `queryFn`, `select`, and `queryKey` properties provided.

```tsx
import { useWorkflows } from './hooks/workflows';

export const Example = () => {
  const { data, isLoading } = useWorkflows({
    /* params */
  });

  // Use `isLoading` value to display a loading indicator
  if (isLoading) return <div>Loading ...</div>;

  // Use `data` value to display the response
  return (
    <div>
      <h1>Here is your data:</h1>
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </div>
  );
};
```

### Suspense

See: [Tanstack Query docs for Suspense](https://tanstack.com/query/latest/docs/framework/react/guides/suspense)

React Query can also be used with React's Suspense for Data Fetching API's. Each generated query hook has a Suspense variant that can be used in place of the standard hook.

```tsx
import { useSuspenseWorkflows } from './hooks/workflows';

export const ExampleContainer = () => (
  // Use suspense to display a loading indicator
  <React.Suspense fallback={<div>Loading...</div>}>
    <Example />
  </React.Suspense>
);

export const Example = () => {
  const { data } = useSuspenseWorkflows({
    /* params */
  });

  // Use `data` value to display the response
  return (
    <div>
      <h1>Here is your data:</h1>
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </div>
  );
};
```

### QueryClient Overrides

Both the standard and suspense hooks can be called with optional client overrides. These options are only applied to the specific query and do not affect the global QueryClient.

```tsx
const { data } = useWorkflows(
  {
    /* params */
  },
  { retry: 5, retryDelay: 1000 },
);

const { data } = useSuspenseWorkflows(
  {
    /* params */
  },
  { retry: 5, retryDelay: 1000 },
);
```

### Infinite Queries

See: [Tanstack Query docs for Infinite Queries](https://tanstack.com/query/latest/docs/framework/react/guides/infinite-queries)

Infinite queries are a special type of query that allows you to fetch new pages of data as the user scrolls or as they trigger a "load more" button. The returned `data` property is a flattened array of all the data fetched so far.

```tsx
import { useInfiniteWorkflows } from './hooks/workflows';

export const Example = () => {
  const { data, isLoading, fetchNext, isFetchingNext } = useInfiniteWorkflows({
    /* params */
  });

  // Use `isLoading` value to display a loading indicator
  if (isLoading) return <div>Loading ...</div>;

  // Use `data` value to display the response
  return (
    <div>
      <h1>Here is your data:</h1>
      <pre>{JSON.stringify(data, null, 2)}</pre>
      <button onClick={fetchNext} disabled={isFetchingNext}>
        Load More
      </button>
    </div>
  );
};
```

Each infinite query has a suspended variant that can be used with React's Suspense for Data Fetching API's. The Suspense variant is used in the same way as the standard infinite query hook.

```tsx
const { data, isLoading, fetchNext, isFetchingNext } = useSuspenseInfiniteWorkflows({
  /* params */
});
```

## Mutations

See: [Tanstack Query docs for Mutations](https://tanstack.com/query/latest/docs/framework/react/guides/mutations)

```tsx
import { useNlSql } from './hooks/nl-to-sqls';

export const Example = () => {
  const { mutate } = useNlSql({
    onSuccess: (data, variables) => {
      console.log('called with variables', variables);
      console.log('returned data', data);
    },
    onError: console.error,
  });

  const handleClick = useCallback(() => {
    mutate({
      /* params */
    });
  }, [mutate]);

  return (
    <div>
      <button onClick={handleClick}>Nl sql</button>
    </div>
  );
};
```

## Error Handling

React Query returns an `error` property from the query and mutation hooks. This value is non-null when an error has been raised.

The generated hooks return an error of type `QueryError<T>` where `T` is the type of error returned from the API method. This error type is a discriminated union of either a handled or unhandled error.

Handled errors will be of type `T` and are generally things like validation errors returned in a structurd format from the API. Unhandled errors are of type `unknown` generally represent exceptions thrown during the execution of the API or the processing of the response.

## Services

The generated hooks make use of the generated HTTP Client service implementations. While hooks provide a React-idiomatic mechanism for interacting with your API, the raw service implmentations provide more precise, fine-gained control.

Using the generated React Query hooks will be sufficient for most use cases; however, the services can be access from within the `TransformProvider` tree by using the hooks exported from the `context` module.

```tsx
import { useCallback } from 'react';
import { useNlToSqlService } from './hooks/context';

export const Example = () => {
  const nlToSqlService = useNlToSqlService();

  const handleClick = useCallback(() => {
    // Do something directly with the nl to sql service
  }, [nlToSqlService]);

  return (
    <div>
      <button onClick={handleClick}>Custom action</button>
    </div>
  );
};
```
