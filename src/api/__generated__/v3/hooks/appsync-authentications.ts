/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  AppsyncAuthentication,
  AppsyncAuthenticationResponse,
  Error,
  GetAppsyncAuthenticationParams,
} from '@floqastinc/transform-v3';
import {
  queryOptions,
  type UndefinedInitialDataOptions,
  useQuery,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { useAppsyncAuthenticationService } from './context';
import { assert, guard, type QueryError } from './runtime';

const useGetAppsyncAuthenticationQueryOptions = (params: GetAppsyncAuthenticationParams) => {
  const appsyncAuthenticationService = useAppsyncAuthenticationService();
  return queryOptions<AppsyncAuthenticationResponse, QueryError<Error[]>, AppsyncAuthentication>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/events/auth`,
    ],
    queryFn: async () => {
      const res = await guard(appsyncAuthenticationService.getAppsyncAuthentication(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get authentication information for AppSync
 */
export function useAppsyncAuthentication(
  params: GetAppsyncAuthenticationParams,
  options?: Omit<
    UndefinedInitialDataOptions<
      AppsyncAuthenticationResponse,
      QueryError<Error[]>,
      AppsyncAuthentication
    >,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetAppsyncAuthenticationQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get authentication information for AppSync
 */
export function useSuspenseAppsyncAuthentication(
  params: GetAppsyncAuthenticationParams,
  options?: Omit<
    UndefinedInitialDataOptions<
      AppsyncAuthenticationResponse,
      QueryError<Error[]>,
      AppsyncAuthentication
    >,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetAppsyncAuthenticationQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
