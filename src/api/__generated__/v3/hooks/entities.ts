/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type { EntitiesResponse, Entity, Error } from '@floqastinc/transform-v3';
import {
  queryOptions,
  type UndefinedInitialDataOptions,
  useQuery,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { useEntityService } from './context';
import { guard, type QueryError } from './runtime';

const useGetEntitiesQueryOptions = () => {
  const entityService = useEntityService();
  return queryOptions<EntitiesResponse, QueryError<Error[]>, Entity[]>({
    queryKey: [`/entities`],
    queryFn: async () => {
      const res = await guard(entityService.getEntities());
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * List all entities
 */
export function useEntities(
  options?: Omit<
    UndefinedInitialDataOptions<EntitiesResponse, QueryError<Error[]>, Entity[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetEntitiesQueryOptions();
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all entities
 */
export function useSuspenseEntities(
  options?: Omit<
    UndefinedInitialDataOptions<EntitiesResponse, QueryError<Error[]>, Entity[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetEntitiesQueryOptions();
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
