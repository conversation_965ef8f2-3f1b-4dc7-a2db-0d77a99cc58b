/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  CreateExampleParams,
  DeleteExampleParams,
  Error,
  ExampleSet,
  ExampleSetResponse,
  ExampleSetsResponse,
  GetExampleLogParams,
  GetExampleParams,
  GetExamplesParams,
  LogResponse,
  UpdateExampleParams,
} from '@floqastinc/transform-v3';
import {
  type InfiniteData,
  queryOptions,
  type UndefinedInitialDataOptions,
  useInfiniteQuery,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseInfiniteQuery,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { useExampleService } from './context';
import {
  applyPageParam,
  assert,
  compact,
  getInitialPageParam,
  getNextPageParam,
  getPreviousPageParam,
  guard,
  type PageParam,
  type QueryError,
} from './runtime';

/**
 * Create a new example set. If workflow and task run IDs are provided, the example set will be created using the task run's inputs.
 */
export function useCreateExample(
  options?: Omit<
    UseMutationOptions<ExampleSet, QueryError<Error[]>, CreateExampleParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const exampleService = useExampleService();
  return useMutation({
    mutationFn: async (params: CreateExampleParams) => {
      const res = await guard(exampleService.createExample(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}/examples`],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Delete an example set
 */
export function useDeleteExample(
  options?: Omit<
    UseMutationOptions<ExampleSet, QueryError<Error[]>, DeleteExampleParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const exampleService = useExampleService();
  return useMutation({
    mutationFn: async (params: DeleteExampleParams) => {
      const res = await guard(exampleService.deleteExample(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}`,
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}/examples`],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetExampleQueryOptions = (params: GetExampleParams) => {
  const exampleService = useExampleService();
  return queryOptions<ExampleSetResponse, QueryError<Error[]>, ExampleSet>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}`,
    ],
    queryFn: async () => {
      const res = await guard(exampleService.getExample(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get an example set
 */
export function useExample(
  params: GetExampleParams,
  options?: Omit<
    UndefinedInitialDataOptions<ExampleSetResponse, QueryError<Error[]>, ExampleSet>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetExampleQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get an example set
 */
export function useSuspenseExample(
  params: GetExampleParams,
  options?: Omit<
    UndefinedInitialDataOptions<ExampleSetResponse, QueryError<Error[]>, ExampleSet>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetExampleQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetExampleLogQueryOptions = (params: GetExampleLogParams) => {
  const exampleService = useExampleService();
  return queryOptions<LogResponse, QueryError<Error[]>>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/log`,
      compact({
        first: params.first,
        after: params.after,
        last: params.last,
        before: params.before,
      }),
    ].filter(Boolean),
    queryFn: async () => {
      const res = await guard(exampleService.getExampleLog(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
  });
};

/**
 * Get the log for an example set
 */
export function useExampleLog(
  params: GetExampleLogParams,
  options?: Omit<
    UndefinedInitialDataOptions<LogResponse, QueryError<Error[]>>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetExampleLogQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get the log for an example set
 */
export function useSuspenseExampleLog(
  params: GetExampleLogParams,
  options?: Omit<
    UndefinedInitialDataOptions<LogResponse, QueryError<Error[]>>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetExampleLogQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
function useInfiniteExampleLogQueryOptions(params: GetExampleLogParams) {
  const exampleService = useExampleService();
  return {
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/log`,
      { inifinite: true },
    ],
    queryFn: async ({ pageParam }: PageParam) => {
      const res = await guard(exampleService.getExampleLog(applyPageParam(params, pageParam)));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data: InfiniteData<LogResponse, string | undefined>) =>
      data.pages.flatMap((page) => page.data),
    initialPageParam: getInitialPageParam(params),
    getNextPageParam,
    getPreviousPageParam,
  };
}

/**
 * Get the log for an example set
 */
export const useInfiniteExampleLog = (params: GetExampleLogParams) => {
  const options = useInfiniteExampleLogQueryOptions(params);
  return useInfiniteQuery(options);
};

/**
 * Get the log for an example set
 */
export const useSuspenseInfiniteExampleLog = (params: GetExampleLogParams) => {
  const options = useInfiniteExampleLogQueryOptions(params);
  return useSuspenseInfiniteQuery(options);
};

const useGetExamplesQueryOptions = (params: GetExamplesParams) => {
  const exampleService = useExampleService();
  return queryOptions<ExampleSetsResponse, QueryError<Error[]>, ExampleSet[]>({
    queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}/examples`],
    queryFn: async () => {
      const res = await guard(exampleService.getExamples(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * List all examples sets for a task
 */
export function useExamples(
  params: GetExamplesParams,
  options?: Omit<
    UndefinedInitialDataOptions<ExampleSetsResponse, QueryError<Error[]>, ExampleSet[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetExamplesQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all examples sets for a task
 */
export function useSuspenseExamples(
  params: GetExamplesParams,
  options?: Omit<
    UndefinedInitialDataOptions<ExampleSetsResponse, QueryError<Error[]>, ExampleSet[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetExamplesQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

/**
 * Update an example set
 */
export function useUpdateExample(
  options?: Omit<
    UseMutationOptions<ExampleSet, QueryError<Error[]>, UpdateExampleParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const exampleService = useExampleService();
  return useMutation({
    mutationFn: async (params: UpdateExampleParams) => {
      const res = await guard(exampleService.updateExample(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}`,
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}/examples`],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}
