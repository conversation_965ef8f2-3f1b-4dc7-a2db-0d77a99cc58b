/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type { Error, Experiment, ExperimentsResponse } from '@floqastinc/transform-v3';
import {
  queryOptions,
  type UndefinedInitialDataOptions,
  useQuery,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { useExperimentService } from './context';
import { guard, type QueryError } from './runtime';

const useGetExperimentsQueryOptions = () => {
  const experimentService = useExperimentService();
  return queryOptions<ExperimentsResponse, QueryError<Error[]>, Experiment[]>({
    queryKey: [`/experiments`],
    queryFn: async () => {
      const res = await guard(experimentService.getExperiments());
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * List all experiments
 */
export function useExperiments(
  options?: Omit<
    UndefinedInitialDataOptions<ExperimentsResponse, QueryError<Error[]>, Experiment[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetExperimentsQueryOptions();
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all experiments
 */
export function useSuspenseExperiments(
  options?: Omit<
    UndefinedInitialDataOptions<ExperimentsResponse, QueryError<Error[]>, Experiment[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetExperimentsQueryOptions();
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
