/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type { CreatePromptParams, EnhancedPromptOutput, Error } from '@floqastinc/transform-v3';
import { useMutation, type UseMutationOptions, useQueryClient } from '@tanstack/react-query';
import { usePromptService } from './context';
import { assert, guard, type QueryError } from './runtime';

/**
 * Create a new message for a thread
 */
export function useCreatePrompt(
  options?: Omit<
    UseMutationOptions<EnhancedPromptOutput, QueryError<Error[]>, CreatePromptParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const promptService = usePromptService();
  return useMutation({
    mutationFn: async (params: CreatePromptParams) => {
      const res = await guard(promptService.createPrompt(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/prompts`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}
