/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  CreateWorkflowRunParams,
  DeepWorkflowRun,
  DeepWorkflowRunResponse,
  DeleteWorkflowRunParams,
  EmptyResponseData,
  Error,
  GetAllWorkflowRunsParams,
  GetDeepWorkflowRunParams,
  GetTaskRunInputParams,
  GetTaskRunInputsParams,
  GetTaskRunInputValueUriParams,
  GetTaskRunLogParams,
  GetTaskRunMessagesParams,
  GetTaskRunOutputParams,
  GetTaskRunOutputsParams,
  GetTaskRunOutputValueParams,
  GetTaskRunOutputValueUriParams,
  GetTaskRunParams,
  GetTaskRunsParams,
  GetWorkflowRunInputParams,
  GetWorkflowRunInputsParams,
  GetWorkflowRunInputValueUriParams,
  GetWorkflowRunOutputParams,
  GetWorkflowRunOutputsParams,
  GetWorkflowRunOutputValueUriParams,
  GetWorkflowRunParams,
  GetWorkflowRunsParams,
  LogResponse,
  MessagesResponse,
  NewWorkflowRunResponseData,
  OutputValue,
  PresignedUriResponseResponse,
  PresignedUrl,
  SetWorkflowRunDatetimeInputParams,
  SetWorkflowRunNumberInputParams,
  SetWorkflowRunTextInputParams,
  StartWorkflowRunParams,
  TaskRun,
  TaskRunInput,
  TaskRunInputResponse,
  TaskRunInputsResponse,
  TaskRunOutput,
  TaskRunOutputResponse,
  TaskRunOutputsResponse,
  TaskRunResponse,
  TaskRunsResponse,
  TickWorkflowRunParams,
  UpdateTaskRunParams,
  UpdateWorkflowRunInputFileParams,
  UpdateWorkflowRunParams,
  ValueResponseResponse,
  WorkflowRun,
  WorkflowRunInput,
  WorkflowRunInputResponse,
  WorkflowRunInputsResponse,
  WorkflowRunOutput,
  WorkflowRunOutputResponse,
  WorkflowRunOutputsResponse,
  WorkflowRunResponse,
  WorkflowRunsResponse,
  WorkflowRunTick,
} from '@floqastinc/transform-v3';
import {
  type InfiniteData,
  queryOptions,
  type UndefinedInitialDataOptions,
  useInfiniteQuery,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseInfiniteQuery,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { useRunService } from './context';
import {
  applyPageParam,
  assert,
  compact,
  getInitialPageParam,
  getNextPageParam,
  getPreviousPageParam,
  guard,
  type PageParam,
  type QueryError,
} from './runtime';

const useGetAllWorkflowRunsQueryOptions = (params?: GetAllWorkflowRunsParams) => {
  const runService = useRunService();
  return queryOptions<WorkflowRunsResponse, QueryError<Error[]>>({
    queryKey: [
      `/runs`,
      compact({
        first: params?.first,
        after: params?.after,
        last: params?.last,
        before: params?.before,
        workflowId: params?.workflowId,
        status: params?.status,
      }),
    ].filter(Boolean),
    queryFn: async () => {
      const res = await guard(runService.getAllWorkflowRuns(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
  });
};

/**
 * List all runs
 */
export function useAllWorkflowRuns(
  params?: GetAllWorkflowRunsParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowRunsResponse, QueryError<Error[]>>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetAllWorkflowRunsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all runs
 */
export function useSuspenseAllWorkflowRuns(
  params?: GetAllWorkflowRunsParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowRunsResponse, QueryError<Error[]>>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetAllWorkflowRunsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
function useInfiniteAllWorkflowRunsQueryOptions(params?: GetAllWorkflowRunsParams) {
  const runService = useRunService();
  return {
    queryKey: [
      `/runs`,
      compact({ workflowId: params?.workflowId, status: params?.status }),
      { inifinite: true },
    ].filter(Boolean),
    queryFn: async ({ pageParam }: PageParam) => {
      const res = await guard(
        runService.getAllWorkflowRuns(applyPageParam(params ?? {}, pageParam)),
      );
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data: InfiniteData<WorkflowRunsResponse, string | undefined>) =>
      data.pages.flatMap((page) => page.data),
    initialPageParam: getInitialPageParam(params ?? {}),
    getNextPageParam,
    getPreviousPageParam,
  };
}

/**
 * List all runs
 */
export const useInfiniteAllWorkflowRuns = (params?: GetAllWorkflowRunsParams) => {
  const options = useInfiniteAllWorkflowRunsQueryOptions(params);
  return useInfiniteQuery(options);
};

/**
 * List all runs
 */
export const useSuspenseInfiniteAllWorkflowRuns = (params?: GetAllWorkflowRunsParams) => {
  const options = useInfiniteAllWorkflowRunsQueryOptions(params);
  return useSuspenseInfiniteQuery(options);
};

/**
 * Create a new workflow run
 */
export function useCreateWorkflowRun(
  options?: Omit<
    UseMutationOptions<NewWorkflowRunResponseData, QueryError<Error[]>, CreateWorkflowRunParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const runService = useRunService();
  return useMutation({
    mutationFn: async (params: CreateWorkflowRunParams) => {
      const res = await guard(runService.createWorkflowRun(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/workflows/${params.workflowId}/runs`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetDeepWorkflowRunQueryOptions = (params: GetDeepWorkflowRunParams) => {
  const runService = useRunService();
  return queryOptions<DeepWorkflowRunResponse, QueryError<Error[]>, DeepWorkflowRun>({
    queryKey: [`/runs/${params.workflowRunId}/deep`],
    queryFn: async () => {
      const res = await guard(runService.getDeepWorkflowRun(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get a workflow run with all related data. In the future, we will be providing this type of deep data fetching via GraphQL. For this reason, this endpoint is deprecated and will be removed in a future version.
 * @deprecated
 */
export function useDeepWorkflowRun(
  params: GetDeepWorkflowRunParams,
  options?: Omit<
    UndefinedInitialDataOptions<DeepWorkflowRunResponse, QueryError<Error[]>, DeepWorkflowRun>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetDeepWorkflowRunQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get a workflow run with all related data. In the future, we will be providing this type of deep data fetching via GraphQL. For this reason, this endpoint is deprecated and will be removed in a future version.
 * @deprecated
 */
export function useSuspenseDeepWorkflowRun(
  params: GetDeepWorkflowRunParams,
  options?: Omit<
    UndefinedInitialDataOptions<DeepWorkflowRunResponse, QueryError<Error[]>, DeepWorkflowRun>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetDeepWorkflowRunQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

/**
 * Delete a run
 */
export function useDeleteWorkflowRun(
  options?: Omit<
    UseMutationOptions<EmptyResponseData, QueryError<Error[]>, DeleteWorkflowRunParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const runService = useRunService();
  return useMutation({
    mutationFn: async (params: DeleteWorkflowRunParams) => {
      const res = await guard(runService.deleteWorkflowRun(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/runs/${params.workflowRunId}`] });
      queryClient.invalidateQueries({ queryKey: [`/runs`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Set an input datetime value
 */
export function useSetWorkflowRunDatetimeInput(
  options?: Omit<
    UseMutationOptions<WorkflowRunInput, QueryError<Error[]>, SetWorkflowRunDatetimeInputParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const runService = useRunService();
  return useMutation({
    mutationFn: async (params: SetWorkflowRunDatetimeInputParams) => {
      const res = await guard(runService.setWorkflowRunDatetimeInput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/runs/${params.workflowRunId}/inputs/${params.workflowRunInputId}/value/datetime`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Set an input number value
 */
export function useSetWorkflowRunNumberInput(
  options?: Omit<
    UseMutationOptions<WorkflowRunInput, QueryError<Error[]>, SetWorkflowRunNumberInputParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const runService = useRunService();
  return useMutation({
    mutationFn: async (params: SetWorkflowRunNumberInputParams) => {
      const res = await guard(runService.setWorkflowRunNumberInput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/runs/${params.workflowRunId}/inputs/${params.workflowRunInputId}/value/number`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Set an input text value
 */
export function useSetWorkflowRunTextInput(
  options?: Omit<
    UseMutationOptions<WorkflowRunInput, QueryError<Error[]>, SetWorkflowRunTextInputParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const runService = useRunService();
  return useMutation({
    mutationFn: async (params: SetWorkflowRunTextInputParams) => {
      const res = await guard(runService.setWorkflowRunTextInput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/runs/${params.workflowRunId}/inputs/${params.workflowRunInputId}/value/text`],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Starts the workflow run which is then executed in a background process.
 */
export function useStartWorkflowRun(
  options?: Omit<
    UseMutationOptions<EmptyResponseData, QueryError<Error[]>, StartWorkflowRunParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const runService = useRunService();
  return useMutation({
    mutationFn: async (params: StartWorkflowRunParams) => {
      const res = await guard(runService.startWorkflowRun(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/runs/${params.workflowRunId}/start`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetTaskRunQueryOptions = (params: GetTaskRunParams) => {
  const runService = useRunService();
  return queryOptions<TaskRunResponse, QueryError<Error[]>, TaskRun>({
    queryKey: [`/runs/${params.workflowRunId}/tasks/${params.taskRunId}`],
    queryFn: async () => {
      const res = await guard(runService.getTaskRun(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get a task run
 */
export function useTaskRun(
  params: GetTaskRunParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskRunResponse, QueryError<Error[]>, TaskRun>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get a task run
 */
export function useSuspenseTaskRun(
  params: GetTaskRunParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskRunResponse, QueryError<Error[]>, TaskRun>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTaskRunInputQueryOptions = (params: GetTaskRunInputParams) => {
  const runService = useRunService();
  return queryOptions<TaskRunInputResponse, QueryError<Error[]>, TaskRunInput>({
    queryKey: [
      `/runs/${params.workflowRunId}/tasks/${params.taskRunId}/inputs/${params.taskRunInputId}`,
    ],
    queryFn: async () => {
      const res = await guard(runService.getTaskRunInput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get a task run input
 */
export function useTaskRunInput(
  params: GetTaskRunInputParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskRunInputResponse, QueryError<Error[]>, TaskRunInput>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunInputQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get a task run input
 */
export function useSuspenseTaskRunInput(
  params: GetTaskRunInputParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskRunInputResponse, QueryError<Error[]>, TaskRunInput>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunInputQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTaskRunInputsQueryOptions = (params: GetTaskRunInputsParams) => {
  const runService = useRunService();
  return queryOptions<TaskRunInputsResponse, QueryError<Error[]>, TaskRunInput[]>({
    queryKey: [`/runs/${params.workflowRunId}/tasks/${params.taskRunId}/inputs`],
    queryFn: async () => {
      const res = await guard(runService.getTaskRunInputs(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * List all inputs for a task run
 */
export function useTaskRunInputs(
  params: GetTaskRunInputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskRunInputsResponse, QueryError<Error[]>, TaskRunInput[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunInputsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all inputs for a task run
 */
export function useSuspenseTaskRunInputs(
  params: GetTaskRunInputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskRunInputsResponse, QueryError<Error[]>, TaskRunInput[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunInputsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTaskRunInputValueUriQueryOptions = (params: GetTaskRunInputValueUriParams) => {
  const runService = useRunService();
  return queryOptions<PresignedUriResponseResponse, QueryError<Error[]>, void>({
    queryKey: [
      `/runs/${params.workflowRunId}/tasks/${params.taskRunId}/inputs/${params.taskRunInputId}/value/uri`,
    ],
    queryFn: async () => {
      const res = await guard(runService.getTaskRunInputValueUri(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * Get the URI for an input value
 */
export function useTaskRunInputValueUri(
  params: GetTaskRunInputValueUriParams,
  options?: Omit<
    UndefinedInitialDataOptions<PresignedUriResponseResponse, QueryError<Error[]>, void>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunInputValueUriQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get the URI for an input value
 */
export function useSuspenseTaskRunInputValueUri(
  params: GetTaskRunInputValueUriParams,
  options?: Omit<
    UndefinedInitialDataOptions<PresignedUriResponseResponse, QueryError<Error[]>, void>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunInputValueUriQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTaskRunLogQueryOptions = (params: GetTaskRunLogParams) => {
  const runService = useRunService();
  return queryOptions<LogResponse, QueryError<Error[]>>({
    queryKey: [
      `/runs/${params.workflowRunId}/tasks/${params.taskRunId}/log`,
      compact({
        first: params.first,
        after: params.after,
        last: params.last,
        before: params.before,
      }),
    ].filter(Boolean),
    queryFn: async () => {
      const res = await guard(runService.getTaskRunLog(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
  });
};

/**
 * Get a task run
 */
export function useTaskRunLog(
  params: GetTaskRunLogParams,
  options?: Omit<
    UndefinedInitialDataOptions<LogResponse, QueryError<Error[]>>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunLogQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get a task run
 */
export function useSuspenseTaskRunLog(
  params: GetTaskRunLogParams,
  options?: Omit<
    UndefinedInitialDataOptions<LogResponse, QueryError<Error[]>>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunLogQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
function useInfiniteTaskRunLogQueryOptions(params: GetTaskRunLogParams) {
  const runService = useRunService();
  return {
    queryKey: [`/runs/${params.workflowRunId}/tasks/${params.taskRunId}/log`, { inifinite: true }],
    queryFn: async ({ pageParam }: PageParam) => {
      const res = await guard(runService.getTaskRunLog(applyPageParam(params, pageParam)));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data: InfiniteData<LogResponse, string | undefined>) =>
      data.pages.flatMap((page) => page.data),
    initialPageParam: getInitialPageParam(params),
    getNextPageParam,
    getPreviousPageParam,
  };
}

/**
 * Get a task run
 */
export const useInfiniteTaskRunLog = (params: GetTaskRunLogParams) => {
  const options = useInfiniteTaskRunLogQueryOptions(params);
  return useInfiniteQuery(options);
};

/**
 * Get a task run
 */
export const useSuspenseInfiniteTaskRunLog = (params: GetTaskRunLogParams) => {
  const options = useInfiniteTaskRunLogQueryOptions(params);
  return useSuspenseInfiniteQuery(options);
};

const useGetTaskRunMessagesQueryOptions = (params: GetTaskRunMessagesParams) => {
  const runService = useRunService();
  return queryOptions<MessagesResponse, QueryError<Error[]>>({
    queryKey: [
      `/runs/${params.workflowRunId}/tasks/${params.taskRunId}/messages`,
      compact({
        first: params.first,
        after: params.after,
        last: params.last,
        before: params.before,
      }),
    ].filter(Boolean),
    queryFn: async () => {
      const res = await guard(runService.getTaskRunMessages(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
  });
};

/**
 * List all messages for a task run thread
 */
export function useTaskRunMessages(
  params: GetTaskRunMessagesParams,
  options?: Omit<
    UndefinedInitialDataOptions<MessagesResponse, QueryError<Error[]>>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunMessagesQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all messages for a task run thread
 */
export function useSuspenseTaskRunMessages(
  params: GetTaskRunMessagesParams,
  options?: Omit<
    UndefinedInitialDataOptions<MessagesResponse, QueryError<Error[]>>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunMessagesQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
function useInfiniteTaskRunMessagesQueryOptions(params: GetTaskRunMessagesParams) {
  const runService = useRunService();
  return {
    queryKey: [
      `/runs/${params.workflowRunId}/tasks/${params.taskRunId}/messages`,
      { inifinite: true },
    ],
    queryFn: async ({ pageParam }: PageParam) => {
      const res = await guard(runService.getTaskRunMessages(applyPageParam(params, pageParam)));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data: InfiniteData<MessagesResponse, string | undefined>) =>
      data.pages.flatMap((page) => page.data ?? []),
    initialPageParam: getInitialPageParam(params),
    getNextPageParam,
    getPreviousPageParam,
  };
}

/**
 * List all messages for a task run thread
 */
export const useInfiniteTaskRunMessages = (params: GetTaskRunMessagesParams) => {
  const options = useInfiniteTaskRunMessagesQueryOptions(params);
  return useInfiniteQuery(options);
};

/**
 * List all messages for a task run thread
 */
export const useSuspenseInfiniteTaskRunMessages = (params: GetTaskRunMessagesParams) => {
  const options = useInfiniteTaskRunMessagesQueryOptions(params);
  return useSuspenseInfiniteQuery(options);
};

const useGetTaskRunOutputQueryOptions = (params: GetTaskRunOutputParams) => {
  const runService = useRunService();
  return queryOptions<TaskRunOutputResponse, QueryError<Error[]>, TaskRunOutput>({
    queryKey: [
      `/runs/${params.workflowRunId}/tasks/${params.taskRunId}/outputs/${params.taskRunOutputId}`,
    ],
    queryFn: async () => {
      const res = await guard(runService.getTaskRunOutput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get a task run output
 */
export function useTaskRunOutput(
  params: GetTaskRunOutputParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskRunOutputResponse, QueryError<Error[]>, TaskRunOutput>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunOutputQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get a task run output
 */
export function useSuspenseTaskRunOutput(
  params: GetTaskRunOutputParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskRunOutputResponse, QueryError<Error[]>, TaskRunOutput>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunOutputQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTaskRunOutputsQueryOptions = (params: GetTaskRunOutputsParams) => {
  const runService = useRunService();
  return queryOptions<TaskRunOutputsResponse, QueryError<Error[]>, TaskRunOutput[]>({
    queryKey: [`/runs/${params.workflowRunId}/tasks/${params.taskRunId}/outputs`],
    queryFn: async () => {
      const res = await guard(runService.getTaskRunOutputs(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * List all outputs for a task run
 */
export function useTaskRunOutputs(
  params: GetTaskRunOutputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskRunOutputsResponse, QueryError<Error[]>, TaskRunOutput[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunOutputsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all outputs for a task run
 */
export function useSuspenseTaskRunOutputs(
  params: GetTaskRunOutputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskRunOutputsResponse, QueryError<Error[]>, TaskRunOutput[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunOutputsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTaskRunOutputValueQueryOptions = (params: GetTaskRunOutputValueParams) => {
  const runService = useRunService();
  return queryOptions<ValueResponseResponse, QueryError<Error[]>, OutputValue>({
    queryKey: [
      `/runs/${params.workflowRunId}/tasks/${params.taskRunId}/outputs/${params.taskRunOutputId}/value`,
    ],
    queryFn: async () => {
      const res = await guard(runService.getTaskRunOutputValue(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Gets a value for a task run output.
 */
export function useTaskRunOutputValue(
  params: GetTaskRunOutputValueParams,
  options?: Omit<
    UndefinedInitialDataOptions<ValueResponseResponse, QueryError<Error[]>, OutputValue>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunOutputValueQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Gets a value for a task run output.
 */
export function useSuspenseTaskRunOutputValue(
  params: GetTaskRunOutputValueParams,
  options?: Omit<
    UndefinedInitialDataOptions<ValueResponseResponse, QueryError<Error[]>, OutputValue>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunOutputValueQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTaskRunOutputValueUriQueryOptions = (params: GetTaskRunOutputValueUriParams) => {
  const runService = useRunService();
  return queryOptions<PresignedUriResponseResponse, QueryError<Error[]>, void>({
    queryKey: [
      `/runs/${params.workflowRunId}/tasks/${params.taskRunId}/outputs/${params.taskRunOutputId}/value/uri`,
    ],
    queryFn: async () => {
      const res = await guard(runService.getTaskRunOutputValueUri(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * Get the URI for an output value
 */
export function useTaskRunOutputValueUri(
  params: GetTaskRunOutputValueUriParams,
  options?: Omit<
    UndefinedInitialDataOptions<PresignedUriResponseResponse, QueryError<Error[]>, void>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunOutputValueUriQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get the URI for an output value
 */
export function useSuspenseTaskRunOutputValueUri(
  params: GetTaskRunOutputValueUriParams,
  options?: Omit<
    UndefinedInitialDataOptions<PresignedUriResponseResponse, QueryError<Error[]>, void>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunOutputValueUriQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTaskRunsQueryOptions = (params: GetTaskRunsParams) => {
  const runService = useRunService();
  return queryOptions<TaskRunsResponse, QueryError<Error[]>, TaskRun[]>({
    queryKey: [`/runs/${params.workflowRunId}/tasks`],
    queryFn: async () => {
      const res = await guard(runService.getTaskRuns(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * List all runs for a workflow
 */
export function useTaskRuns(
  params: GetTaskRunsParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskRunsResponse, QueryError<Error[]>, TaskRun[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all runs for a workflow
 */
export function useSuspenseTaskRuns(
  params: GetTaskRunsParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskRunsResponse, QueryError<Error[]>, TaskRun[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskRunsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

/**
 * Executes an iteration of the workflow run and returns a value representing the progress.
 * @deprecated
 */
export function useTickWorkflowRun(
  options?: Omit<
    UseMutationOptions<WorkflowRunTick, QueryError<Error[]>, TickWorkflowRunParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const runService = useRunService();
  return useMutation({
    mutationFn: async (params: TickWorkflowRunParams) => {
      const res = await guard(runService.tickWorkflowRun(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/runs/${params.workflowRunId}/tick`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Update a task run
 */
export function useUpdateTaskRun(
  options?: Omit<
    UseMutationOptions<TaskRun, QueryError<Error[]>, UpdateTaskRunParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const runService = useRunService();
  return useMutation({
    mutationFn: async (params: UpdateTaskRunParams) => {
      const res = await guard(runService.updateTaskRun(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/runs/${params.workflowRunId}/tasks/${params.taskRunId}`],
      });
      queryClient.invalidateQueries({ queryKey: [`/runs/${params.workflowRunId}/tasks`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Update a run
 */
export function useUpdateWorkflowRun(
  options?: Omit<
    UseMutationOptions<WorkflowRun, QueryError<Error[]>, UpdateWorkflowRunParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const runService = useRunService();
  return useMutation({
    mutationFn: async (params: UpdateWorkflowRunParams) => {
      const res = await guard(runService.updateWorkflowRun(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/runs/${params.workflowRunId}`] });
      queryClient.invalidateQueries({ queryKey: [`/runs`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Update run document with file meta and generate a presigned url to upload a file to S3
 */
export function useUpdateWorkflowRunInputFile(
  options?: Omit<
    UseMutationOptions<PresignedUrl, QueryError<Error[]>, UpdateWorkflowRunInputFileParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const runService = useRunService();
  return useMutation({
    mutationFn: async (params: UpdateWorkflowRunInputFileParams) => {
      const res = await guard(runService.updateWorkflowRunInputFile(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/runs/${params.workflowRunId}/inputs/${params.workflowRunInputId}/files`],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetWorkflowRunQueryOptions = (params: GetWorkflowRunParams) => {
  const runService = useRunService();
  return queryOptions<WorkflowRunResponse, QueryError<Error[]>, WorkflowRun>({
    queryKey: [`/runs/${params.workflowRunId}`],
    queryFn: async () => {
      const res = await guard(runService.getWorkflowRun(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get a run
 */
export function useWorkflowRun(
  params: GetWorkflowRunParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowRunResponse, QueryError<Error[]>, WorkflowRun>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowRunQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get a run
 */
export function useSuspenseWorkflowRun(
  params: GetWorkflowRunParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowRunResponse, QueryError<Error[]>, WorkflowRun>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowRunQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetWorkflowRunInputQueryOptions = (params: GetWorkflowRunInputParams) => {
  const runService = useRunService();
  return queryOptions<WorkflowRunInputResponse, QueryError<Error[]>, WorkflowRunInput>({
    queryKey: [`/runs/${params.workflowRunId}/inputs/${params.workflowRunInputId}`],
    queryFn: async () => {
      const res = await guard(runService.getWorkflowRunInput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get an input
 */
export function useWorkflowRunInput(
  params: GetWorkflowRunInputParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowRunInputResponse, QueryError<Error[]>, WorkflowRunInput>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowRunInputQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get an input
 */
export function useSuspenseWorkflowRunInput(
  params: GetWorkflowRunInputParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowRunInputResponse, QueryError<Error[]>, WorkflowRunInput>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowRunInputQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetWorkflowRunInputsQueryOptions = (params: GetWorkflowRunInputsParams) => {
  const runService = useRunService();
  return queryOptions<WorkflowRunInputsResponse, QueryError<Error[]>, WorkflowRunInput[]>({
    queryKey: [`/runs/${params.workflowRunId}/inputs`],
    queryFn: async () => {
      const res = await guard(runService.getWorkflowRunInputs(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * List all inputs for a run
 */
export function useWorkflowRunInputs(
  params: GetWorkflowRunInputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowRunInputsResponse, QueryError<Error[]>, WorkflowRunInput[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowRunInputsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all inputs for a run
 */
export function useSuspenseWorkflowRunInputs(
  params: GetWorkflowRunInputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowRunInputsResponse, QueryError<Error[]>, WorkflowRunInput[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowRunInputsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetWorkflowRunInputValueUriQueryOptions = (params: GetWorkflowRunInputValueUriParams) => {
  const runService = useRunService();
  return queryOptions<PresignedUriResponseResponse, QueryError<Error[]>, void>({
    queryKey: [`/runs/${params.workflowRunId}/inputs/${params.workflowRunInputId}/value/uri`],
    queryFn: async () => {
      const res = await guard(runService.getWorkflowRunInputValueUri(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * Get the URI for an input value
 */
export function useWorkflowRunInputValueUri(
  params: GetWorkflowRunInputValueUriParams,
  options?: Omit<
    UndefinedInitialDataOptions<PresignedUriResponseResponse, QueryError<Error[]>, void>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowRunInputValueUriQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get the URI for an input value
 */
export function useSuspenseWorkflowRunInputValueUri(
  params: GetWorkflowRunInputValueUriParams,
  options?: Omit<
    UndefinedInitialDataOptions<PresignedUriResponseResponse, QueryError<Error[]>, void>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowRunInputValueUriQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetWorkflowRunOutputQueryOptions = (params: GetWorkflowRunOutputParams) => {
  const runService = useRunService();
  return queryOptions<WorkflowRunOutputResponse, QueryError<Error[]>, WorkflowRunOutput>({
    queryKey: [`/runs/${params.workflowRunId}/outputs/${params.workflowRunOutputId}`],
    queryFn: async () => {
      const res = await guard(runService.getWorkflowRunOutput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get an output
 */
export function useWorkflowRunOutput(
  params: GetWorkflowRunOutputParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowRunOutputResponse, QueryError<Error[]>, WorkflowRunOutput>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowRunOutputQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get an output
 */
export function useSuspenseWorkflowRunOutput(
  params: GetWorkflowRunOutputParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowRunOutputResponse, QueryError<Error[]>, WorkflowRunOutput>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowRunOutputQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetWorkflowRunOutputsQueryOptions = (params: GetWorkflowRunOutputsParams) => {
  const runService = useRunService();
  return queryOptions<WorkflowRunOutputsResponse, QueryError<Error[]>, WorkflowRunOutput[]>({
    queryKey: [`/runs/${params.workflowRunId}/outputs`],
    queryFn: async () => {
      const res = await guard(runService.getWorkflowRunOutputs(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * List all outputs for a run
 */
export function useWorkflowRunOutputs(
  params: GetWorkflowRunOutputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<
      WorkflowRunOutputsResponse,
      QueryError<Error[]>,
      WorkflowRunOutput[]
    >,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowRunOutputsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all outputs for a run
 */
export function useSuspenseWorkflowRunOutputs(
  params: GetWorkflowRunOutputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<
      WorkflowRunOutputsResponse,
      QueryError<Error[]>,
      WorkflowRunOutput[]
    >,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowRunOutputsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetWorkflowRunOutputValueUriQueryOptions = (
  params: GetWorkflowRunOutputValueUriParams,
) => {
  const runService = useRunService();
  return queryOptions<PresignedUriResponseResponse, QueryError<Error[]>, void>({
    queryKey: [`/runs/${params.workflowRunId}/outputs/${params.workflowRunOutputId}/value/uri`],
    queryFn: async () => {
      const res = await guard(runService.getWorkflowRunOutputValueUri(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * Get an output
 */
export function useWorkflowRunOutputValueUri(
  params: GetWorkflowRunOutputValueUriParams,
  options?: Omit<
    UndefinedInitialDataOptions<PresignedUriResponseResponse, QueryError<Error[]>, void>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowRunOutputValueUriQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get an output
 */
export function useSuspenseWorkflowRunOutputValueUri(
  params: GetWorkflowRunOutputValueUriParams,
  options?: Omit<
    UndefinedInitialDataOptions<PresignedUriResponseResponse, QueryError<Error[]>, void>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowRunOutputValueUriQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetWorkflowRunsQueryOptions = (params: GetWorkflowRunsParams) => {
  const runService = useRunService();
  return queryOptions<WorkflowRunsResponse, QueryError<Error[]>>({
    queryKey: [
      `/workflows/${params.workflowId}/runs`,
      compact({
        first: params.first,
        after: params.after,
        last: params.last,
        before: params.before,
        runType: params.runType,
      }),
    ].filter(Boolean),
    queryFn: async () => {
      const res = await guard(runService.getWorkflowRuns(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
  });
};

/**
 * List all runs for a workflow
 */
export function useWorkflowRuns(
  params: GetWorkflowRunsParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowRunsResponse, QueryError<Error[]>>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowRunsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all runs for a workflow
 */
export function useSuspenseWorkflowRuns(
  params: GetWorkflowRunsParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowRunsResponse, QueryError<Error[]>>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowRunsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
function useInfiniteWorkflowRunsQueryOptions(params: GetWorkflowRunsParams) {
  const runService = useRunService();
  return {
    queryKey: [
      `/workflows/${params.workflowId}/runs`,
      compact({ runType: params.runType }),
      { inifinite: true },
    ].filter(Boolean),
    queryFn: async ({ pageParam }: PageParam) => {
      const res = await guard(runService.getWorkflowRuns(applyPageParam(params, pageParam)));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data: InfiniteData<WorkflowRunsResponse, string | undefined>) =>
      data.pages.flatMap((page) => page.data),
    initialPageParam: getInitialPageParam(params),
    getNextPageParam,
    getPreviousPageParam,
  };
}

/**
 * List all runs for a workflow
 */
export const useInfiniteWorkflowRuns = (params: GetWorkflowRunsParams) => {
  const options = useInfiniteWorkflowRunsQueryOptions(params);
  return useInfiniteQuery(options);
};

/**
 * List all runs for a workflow
 */
export const useSuspenseInfiniteWorkflowRuns = (params: GetWorkflowRunsParams) => {
  const options = useInfiniteWorkflowRunsQueryOptions(params);
  return useSuspenseInfiniteQuery(options);
};
