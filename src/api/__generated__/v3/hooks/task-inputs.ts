/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  CreateTaskInputParams,
  DeleteTaskInputParams,
  Error,
  GetTaskInputParams,
  GetTaskInputSourcesParams,
  GetTaskInputsParams,
  TaskInput,
  TaskInputResponse,
  TaskInputSource,
  TaskInputSourcesResponse,
  TaskInputsResponse,
  UpdateTaskInputParams,
} from '@floqastinc/transform-v3';
import {
  queryOptions,
  type UndefinedInitialDataOptions,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { useTaskInputService } from './context';
import { assert, guard, type QueryError } from './runtime';

/**
 * Create a new input
 */
export function useCreateTaskInput(
  options?: Omit<
    UseMutationOptions<TaskInput, QueryError<Error[]>, CreateTaskInputParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const taskInputService = useTaskInputService();
  return useMutation({
    mutationFn: async (params: CreateTaskInputParams) => {
      const res = await guard(taskInputService.createTaskInput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}/inputs`],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Delete an input
 */
export function useDeleteTaskInput(
  options?: Omit<
    UseMutationOptions<TaskInput, QueryError<Error[]>, DeleteTaskInputParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const taskInputService = useTaskInputService();
  return useMutation({
    mutationFn: async (params: DeleteTaskInputParams) => {
      const res = await guard(taskInputService.deleteTaskInput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/inputs/${params.taskInputId}`,
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}/inputs`],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetTaskInputQueryOptions = (params: GetTaskInputParams) => {
  const taskInputService = useTaskInputService();
  return queryOptions<TaskInputResponse, QueryError<Error[]>, TaskInput>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/inputs/${params.taskInputId}`,
    ],
    queryFn: async () => {
      const res = await guard(taskInputService.getTaskInput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get an input
 */
export function useTaskInput(
  params: GetTaskInputParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskInputResponse, QueryError<Error[]>, TaskInput>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskInputQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get an input
 */
export function useSuspenseTaskInput(
  params: GetTaskInputParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskInputResponse, QueryError<Error[]>, TaskInput>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskInputQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTaskInputsQueryOptions = (params: GetTaskInputsParams) => {
  const taskInputService = useTaskInputService();
  return queryOptions<TaskInputsResponse, QueryError<Error[]>, TaskInput[]>({
    queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}/inputs`],
    queryFn: async () => {
      const res = await guard(taskInputService.getTaskInputs(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * List all inputs for a task
 */
export function useTaskInputs(
  params: GetTaskInputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskInputsResponse, QueryError<Error[]>, TaskInput[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskInputsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all inputs for a task
 */
export function useSuspenseTaskInputs(
  params: GetTaskInputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskInputsResponse, QueryError<Error[]>, TaskInput[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskInputsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTaskInputSourcesQueryOptions = (params: GetTaskInputSourcesParams) => {
  const taskInputService = useTaskInputService();
  return queryOptions<TaskInputSourcesResponse, QueryError<Error[]>, TaskInputSource[]>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/inputs/${params.taskInputId}/sources`,
    ],
    queryFn: async () => {
      const res = await guard(taskInputService.getTaskInputSources(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get any existing values that can be used as sources for example of this input
 */
export function useTaskInputSources(
  params: GetTaskInputSourcesParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskInputSourcesResponse, QueryError<Error[]>, TaskInputSource[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskInputSourcesQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get any existing values that can be used as sources for example of this input
 */
export function useSuspenseTaskInputSources(
  params: GetTaskInputSourcesParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskInputSourcesResponse, QueryError<Error[]>, TaskInputSource[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskInputSourcesQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

/**
 * Update an input
 */
export function useUpdateTaskInput(
  options?: Omit<
    UseMutationOptions<TaskInput, QueryError<Error[]>, UpdateTaskInputParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const taskInputService = useTaskInputService();
  return useMutation({
    mutationFn: async (params: UpdateTaskInputParams) => {
      const res = await guard(taskInputService.updateTaskInput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/inputs/${params.taskInputId}`,
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}/inputs`],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}
