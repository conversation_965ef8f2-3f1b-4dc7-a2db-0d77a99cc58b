/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  CreateTaskOutputParams,
  DeleteTaskOutputParams,
  Error,
  GetTaskOutputParams,
  GetTaskOutputsParams,
  TaskOutput,
  TaskOutputResponse,
  TaskOutputsResponse,
  UpdateTaskOutputParams,
} from '@floqastinc/transform-v3';
import {
  queryOptions,
  type UndefinedInitialDataOptions,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { useTaskOutputService } from './context';
import { assert, guard, type QueryError } from './runtime';

/**
 * Create a new output
 */
export function useCreateTaskOutput(
  options?: Omit<
    UseMutationOptions<TaskOutput, QueryError<Error[]>, CreateTaskOutputParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const taskOutputService = useTaskOutputService();
  return useMutation({
    mutationFn: async (params: CreateTaskOutputParams) => {
      const res = await guard(taskOutputService.createTaskOutput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}/outputs`],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Delete an output
 */
export function useDeleteTaskOutput(
  options?: Omit<
    UseMutationOptions<TaskOutput, QueryError<Error[]>, DeleteTaskOutputParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const taskOutputService = useTaskOutputService();
  return useMutation({
    mutationFn: async (params: DeleteTaskOutputParams) => {
      const res = await guard(taskOutputService.deleteTaskOutput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/outputs/${params.taskOutputId}`,
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}/outputs`],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetTaskOutputQueryOptions = (params: GetTaskOutputParams) => {
  const taskOutputService = useTaskOutputService();
  return queryOptions<TaskOutputResponse, QueryError<Error[]>, TaskOutput>({
    queryKey: [
      `/workflows/${params.workflowId}/tasks/${params.taskId}/outputs/${params.taskOutputId}`,
    ],
    queryFn: async () => {
      const res = await guard(taskOutputService.getTaskOutput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get an output
 */
export function useTaskOutput(
  params: GetTaskOutputParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskOutputResponse, QueryError<Error[]>, TaskOutput>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskOutputQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get an output
 */
export function useSuspenseTaskOutput(
  params: GetTaskOutputParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskOutputResponse, QueryError<Error[]>, TaskOutput>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskOutputQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTaskOutputsQueryOptions = (params: GetTaskOutputsParams) => {
  const taskOutputService = useTaskOutputService();
  return queryOptions<TaskOutputsResponse, QueryError<Error[]>, TaskOutput[]>({
    queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}/outputs`],
    queryFn: async () => {
      const res = await guard(taskOutputService.getTaskOutputs(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * List all outputs for a task
 */
export function useTaskOutputs(
  params: GetTaskOutputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskOutputsResponse, QueryError<Error[]>, TaskOutput[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskOutputsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all outputs for a task
 */
export function useSuspenseTaskOutputs(
  params: GetTaskOutputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskOutputsResponse, QueryError<Error[]>, TaskOutput[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskOutputsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

/**
 * Update an output
 */
export function useUpdateTaskOutput(
  options?: Omit<
    UseMutationOptions<TaskOutput, QueryError<Error[]>, UpdateTaskOutputParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const taskOutputService = useTaskOutputService();
  return useMutation({
    mutationFn: async (params: UpdateTaskOutputParams) => {
      const res = await guard(taskOutputService.updateTaskOutput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/outputs/${params.taskOutputId}`,
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}/outputs`],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}
