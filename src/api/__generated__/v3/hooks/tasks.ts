/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  CreateTaskParams,
  DeleteTaskParams,
  Error,
  GetTaskParams,
  GetTaskScriptParams,
  GetTaskSourcesParams,
  GetTasksParams,
  Task,
  TaskInputSource,
  TaskInputSourcesResponse,
  TaskResponse,
  TaskScript,
  TaskScriptResponse,
  TasksResponse,
  UpdateTaskParams,
  UpdateTaskScriptParams,
} from '@floqastinc/transform-v3';
import {
  queryOptions,
  type UndefinedInitialDataOptions,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { useTaskService } from './context';
import { assert, guard, type QueryError } from './runtime';

/**
 * Create a new task
 */
export function useCreateTask(
  options?: Omit<UseMutationOptions<Task, QueryError<Error[]>, CreateTaskParams>, 'mutationFn'>,
) {
  const queryClient = useQueryClient();
  const taskService = useTaskService();
  return useMutation({
    mutationFn: async (params: CreateTaskParams) => {
      const res = await guard(taskService.createTask(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/workflows/${params.workflowId}/tasks`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Delete a task
 */
export function useDeleteTask(
  options?: Omit<UseMutationOptions<Task, QueryError<Error[]>, DeleteTaskParams>, 'mutationFn'>,
) {
  const queryClient = useQueryClient();
  const taskService = useTaskService();
  return useMutation({
    mutationFn: async (params: DeleteTaskParams) => {
      const res = await guard(taskService.deleteTask(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}`],
      });
      queryClient.invalidateQueries({ queryKey: [`/workflows/${params.workflowId}/tasks`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetTaskQueryOptions = (params: GetTaskParams) => {
  const taskService = useTaskService();
  return queryOptions<TaskResponse, QueryError<Error[]>, Task>({
    queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}`],
    queryFn: async () => {
      const res = await guard(taskService.getTask(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get a task
 */
export function useTask(
  params: GetTaskParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskResponse, QueryError<Error[]>, Task>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get a task
 */
export function useSuspenseTask(
  params: GetTaskParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskResponse, QueryError<Error[]>, Task>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTasksQueryOptions = (params: GetTasksParams) => {
  const taskService = useTaskService();
  return queryOptions<TasksResponse, QueryError<Error[]>, Task[]>({
    queryKey: [`/workflows/${params.workflowId}/tasks`],
    queryFn: async () => {
      const res = await guard(taskService.getTasks(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * List all tasks for a workflow
 */
export function useTasks(
  params: GetTasksParams,
  options?: Omit<
    UndefinedInitialDataOptions<TasksResponse, QueryError<Error[]>, Task[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTasksQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all tasks for a workflow
 */
export function useSuspenseTasks(
  params: GetTasksParams,
  options?: Omit<
    UndefinedInitialDataOptions<TasksResponse, QueryError<Error[]>, Task[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTasksQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTaskScriptQueryOptions = (params: GetTaskScriptParams) => {
  const taskService = useTaskService();
  return queryOptions<TaskScriptResponse, QueryError<Error[]>, TaskScript>({
    queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}/scripts`],
    queryFn: async () => {
      const res = await guard(taskService.getTaskScript(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get the script for a task
 */
export function useTaskScript(
  params: GetTaskScriptParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskScriptResponse, QueryError<Error[]>, TaskScript>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskScriptQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get the script for a task
 */
export function useSuspenseTaskScript(
  params: GetTaskScriptParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskScriptResponse, QueryError<Error[]>, TaskScript>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskScriptQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetTaskSourcesQueryOptions = (params: GetTaskSourcesParams) => {
  const taskService = useTaskService();
  return queryOptions<TaskInputSourcesResponse, QueryError<Error[]>, TaskInputSource[]>({
    queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}/sources`],
    queryFn: async () => {
      const res = await guard(taskService.getTaskSources(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * List all inputs for all tasks
 */
export function useTaskSources(
  params: GetTaskSourcesParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskInputSourcesResponse, QueryError<Error[]>, TaskInputSource[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskSourcesQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all inputs for all tasks
 */
export function useSuspenseTaskSources(
  params: GetTaskSourcesParams,
  options?: Omit<
    UndefinedInitialDataOptions<TaskInputSourcesResponse, QueryError<Error[]>, TaskInputSource[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetTaskSourcesQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

/**
 * Update a task
 */
export function useUpdateTask(
  options?: Omit<UseMutationOptions<Task, QueryError<Error[]>, UpdateTaskParams>, 'mutationFn'>,
) {
  const queryClient = useQueryClient();
  const taskService = useTaskService();
  return useMutation({
    mutationFn: async (params: UpdateTaskParams) => {
      const res = await guard(taskService.updateTask(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}`],
      });
      queryClient.invalidateQueries({ queryKey: [`/workflows/${params.workflowId}/tasks`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Update the script for a task
 */
export function useUpdateTaskScript(
  options?: Omit<
    UseMutationOptions<TaskScript, QueryError<Error[]>, UpdateTaskScriptParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const taskService = useTaskService();
  return useMutation({
    mutationFn: async (params: UpdateTaskScriptParams) => {
      const res = await guard(taskService.updateTaskScript(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/tasks/${params.taskId}/scripts`],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}
