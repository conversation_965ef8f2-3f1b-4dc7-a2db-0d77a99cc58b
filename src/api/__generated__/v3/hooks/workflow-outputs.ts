/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  CreateWorkflowOutputParams,
  DeleteWorkflowOutputParams,
  Error,
  GetWorkflowOutputParams,
  GetWorkflowOutputsParams,
  UpdateWorkflowOutputParams,
  WorkflowOutput,
  WorkflowOutputResponse,
  WorkflowOutputsResponse,
} from '@floqastinc/transform-v3';
import {
  queryOptions,
  type UndefinedInitialDataOptions,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { useWorkflowOutputService } from './context';
import { assert, guard, type QueryError } from './runtime';

/**
 * Create a new output
 */
export function useCreateWorkflowOutput(
  options?: Omit<
    UseMutationOptions<WorkflowOutput, QueryError<Error[]>, CreateWorkflowOutputParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const workflowOutputService = useWorkflowOutputService();
  return useMutation({
    mutationFn: async (params: CreateWorkflowOutputParams) => {
      const res = await guard(workflowOutputService.createWorkflowOutput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/workflows/${params.workflowId}/outputs`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Delete an output
 */
export function useDeleteWorkflowOutput(
  options?: Omit<
    UseMutationOptions<WorkflowOutput, QueryError<Error[]>, DeleteWorkflowOutputParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const workflowOutputService = useWorkflowOutputService();
  return useMutation({
    mutationFn: async (params: DeleteWorkflowOutputParams) => {
      const res = await guard(workflowOutputService.deleteWorkflowOutput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/outputs/${params.workflowOutputId}`],
      });
      queryClient.invalidateQueries({ queryKey: [`/workflows/${params.workflowId}/outputs`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Update an output
 */
export function useUpdateWorkflowOutput(
  options?: Omit<
    UseMutationOptions<WorkflowOutput, QueryError<Error[]>, UpdateWorkflowOutputParams>,
    'mutationFn'
  >,
) {
  const queryClient = useQueryClient();
  const workflowOutputService = useWorkflowOutputService();
  return useMutation({
    mutationFn: async (params: UpdateWorkflowOutputParams) => {
      const res = await guard(workflowOutputService.updateWorkflowOutput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [`/workflows/${params.workflowId}/outputs/${params.workflowOutputId}`],
      });
      queryClient.invalidateQueries({ queryKey: [`/workflows/${params.workflowId}/outputs`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetWorkflowOutputQueryOptions = (params: GetWorkflowOutputParams) => {
  const workflowOutputService = useWorkflowOutputService();
  return queryOptions<WorkflowOutputResponse, QueryError<Error[]>, WorkflowOutput>({
    queryKey: [`/workflows/${params.workflowId}/outputs/${params.workflowOutputId}`],
    queryFn: async () => {
      const res = await guard(workflowOutputService.getWorkflowOutput(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get an output
 */
export function useWorkflowOutput(
  params: GetWorkflowOutputParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowOutputResponse, QueryError<Error[]>, WorkflowOutput>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowOutputQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get an output
 */
export function useSuspenseWorkflowOutput(
  params: GetWorkflowOutputParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowOutputResponse, QueryError<Error[]>, WorkflowOutput>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowOutputQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetWorkflowOutputsQueryOptions = (params: GetWorkflowOutputsParams) => {
  const workflowOutputService = useWorkflowOutputService();
  return queryOptions<WorkflowOutputsResponse, QueryError<Error[]>, WorkflowOutput[]>({
    queryKey: [`/workflows/${params.workflowId}/outputs`],
    queryFn: async () => {
      const res = await guard(workflowOutputService.getWorkflowOutputs(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: 'handled', payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => data.data,
  });
};

/**
 * List all outputs for a workflow
 */
export function useWorkflowOutputs(
  params: GetWorkflowOutputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowOutputsResponse, QueryError<Error[]>, WorkflowOutput[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowOutputsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all outputs for a workflow
 */
export function useSuspenseWorkflowOutputs(
  params: GetWorkflowOutputsParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowOutputsResponse, QueryError<Error[]>, WorkflowOutput[]>,
    'queryKey' | 'queryFn' | 'select'
  >,
) {
  const defaultOptions = useGetWorkflowOutputsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
