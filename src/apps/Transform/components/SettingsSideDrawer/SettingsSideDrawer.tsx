import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Spin<PERSON> } from '@floqastinc/flow-ui_core';
import { Workflow } from '@floqastinc/transform-v3';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import { Experiments } from '../Experiments';
import * as Styled from './SettingsSideDrawer.styles';
import { Modes } from './ui/Modes';
import { t } from '@/utils/i18n';
import { getWorkflowQuery } from '@BuilderV3/api/workflows';
import { useUpdateWorkflow } from '@v3/workflows';
import { useCreateOrUpdateExperimentAssignment } from '@BuilderV3/api/experiments';

interface DrawerContentProps {
  workflow: Workflow;
  onClose: (show: boolean) => void;
}
function DrawerContent({ workflow: oldWorkflow, onClose }: DrawerContentProps) {
  const updateWorkflow = useUpdateWorkflow();
  const updateWorkflowExperiments = useCreateOrUpdateExperimentAssignment(oldWorkflow.id);

  const [workflow, setWorkflow] = useState<Workflow>(oldWorkflow);
  const [experimentChanges, setExperimentChanges] = useState<Record<string, string>>({});
  const setSettings = (name: keyof Workflow['settings'], value: boolean) => {
    setWorkflow({
      ...workflow,
      settings: {
        ...workflow.settings,
        [name]: value,
      },
    });
  };

  const handleUpdate = () => {
    updateWorkflow.mutateAsync({ workflow: workflow, workflowId: workflow.id });
    updateWorkflowExperiments.mutateAsync(
      Object.entries(experimentChanges).map(([experimentName, variantId]) => ({
        experimentName,
        variantId,
      })),
    );
  };

  const handleExperimentUpdates = (experimentName: string, variantId: string) => {
    setExperimentChanges((prev) => ({
      ...prev,
      [experimentName]: variantId,
    }));
  };

  return (
    <>
      <SideDrawer.Header>
        <SideDrawer.Title>{t('generics.settings')}</SideDrawer.Title>
        <SideDrawer.TopRight>
          <CloseButton onClick={onClose} aria-label={t('generics.close')} />
        </SideDrawer.TopRight>
      </SideDrawer.Header>
      <Styled.SideDrawerBody>
        <Modes settings={workflow.settings} setSettings={setSettings} />
        <Experiments onChange={handleExperimentUpdates} workflowId={workflow.id} />
      </Styled.SideDrawerBody>
      <Styled.SideDrawerFooter>
        <Button onClick={handleUpdate}>{t('generics.authorize')}</Button>
      </Styled.SideDrawerFooter>
    </>
  );
}

interface SettingsSideDrawerProps {
  workflowId: string;
  show: boolean;
  onClose: (show: boolean) => void;
}
export function SettingsSideDrawer({ workflowId, show, onClose }: SettingsSideDrawerProps) {
  const { data, isLoading } = useQuery(getWorkflowQuery(workflowId));

  return (
    <SideDrawer aria-label="" show={show} renderOverlay={show} onCancel={onClose}>
      <SideDrawer.Overlay>
        {isLoading && (
          <Styled.LoadingPage>
            <Spinner color="success" size={48} />
          </Styled.LoadingPage>
        )}
        {data && <DrawerContent workflow={data} onClose={onClose} />}
      </SideDrawer.Overlay>
    </SideDrawer>
  );
}
