import { Dialog, useToast } from '@floqastinc/flow-ui_core';
import { useMutation } from '@tanstack/react-query';
import { t } from '@/utils/i18n';
import { useParams } from 'react-router-dom';
import { match } from 'ts-pattern';
import v3 from '@/services/v3';
import { editMessage, regenerateMessage } from '@/api/bff/messages';

type ConfirmationDialogProps = {
  message: any;
  action: 'edit' | 'delete' | 'regenerate';
  onConfirm: () => void;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
};
export const ConfirmationDialog = ({
  message,
  action,
  isOpen,
  onOpenChange,
  onConfirm,
}: ConfirmationDialogProps) => {
  const { workflowId = '', taskId = '', exampleSetId = '' } = useParams();

  const { showToast, Toast } = useToast();
  type ToastType = 'error' | 'success';
  const renderToast = (type: ToastType, title: string, message: string) => {
    showToast(
      <Toast type={type}>
        <Toast.Title>{title}</Toast.Title>
        <Toast.Message>{message}</Toast.Message>
      </Toast>,
      {
        duration: 5000,
        position: 'bottom-right',
      },
    );
  };

  const editMessageMutation = useMutation({
    mutationKey: [
      'editMessage',
      {
        workflowId,
        taskId,
        exampleSetId,
        messageId: message.id,
      },
    ],
    mutationFn: async (message: { content: string; id: string }) => {
      setMessages([
        ...removeMessagesFrom(message.id),
        {
          id: undefined,
          role: 'user',
          content: message.content,
        },
      ]);
      // TODO: Add some validation here
      return editMessage({
        workflowId,
        taskId: taskId,
        exampleSetId,
        message,
      });
    },
    onError: (error) => {
      console.error('error', error);
    },
  });

  const deleteMessageMutation = useMutation({
    mutationKey: [
      'deleteMessage',
      {
        workflowId,
        taskId,
        exampleSetId,
        messageId: message.id,
      },
    ],
    mutationFn: async (messageId: string) => {
      return v3.messages.deleteMessage({
        workflowId,
        taskId,
        exampleSetId,
        messageId,
      });
    },
    onSuccess: async () => {
      renderToast('success', 'Success', t('components.Builder.messageDeleted'));
    },
    onError: (error) => {
      console.error('error', error);
    },
  });

  const regenerateMessageMutation = useMutation({
    mutationKey: [
      'regenerateMessage',
      {
        workflowId,
        taskId,
        exampleSetId,
        messageId: message.id,
      },
    ],
    mutationFn: async (messageId: string) => {
      setMessages(removeMessagesAfter(messageId));
      return regenerateMessage({
        workflowId,
        taskId: taskId,
        exampleSetId,
        messageId,
      });
    },
    onSuccess: async () => {
      renderToast('success', 'Success', t('components.Builder.messageRegenerated'));
    },
    onError: (error) => {
      console.error('error', error);
    },
  });

  const confirmationDialogContent = match(action)
    .with('edit', () => ({
      header: 'Edit Message',
      body: t('components.Builder.editMessage'),
      action: () => {
        if (!message.id) {
          throw new Error(t('components.Builder.Errors.noMessageID'));
        }
        editMessageMutation.mutate({
          content: draftMessage,
          id: message.id,
        });
      },
    }))
    .with('delete', () => ({
      header: 'Delete Message',
      body: t('components.Builder.deleteMessage'),
      action: () => {
        if (!message.id) {
          throw new Error(t('components.Builder.Errors.noMessageID'));
        }
        deleteMessageMutation.mutate(message.id);
      },
    }))
    .with('regenerate', () => ({
      header: 'Regenerate Assistant Response',
      body: t('components.Builder.areYouSureRegenerateResponse'),
      action: () => {
        if (!message.id) {
          throw new Error(t('components.Builder.Errors.noMessageID'));
        }
        regenerateMessageMutation.mutate(message.id);
      },
    }))
    .exhaustive();

  return isOpen ? (
    <Dialog
      type="warning"
      open={action !== null}
      onOpenChange={(isOpen: boolean) => {
        if (!isOpen) {
          onOpenChange(false);
        }
      }}
    >
      <Dialog.Header>{confirmationDialogContent.header}</Dialog.Header>
      <Dialog.Body>{confirmationDialogContent.body}</Dialog.Body>
      <Dialog.Footer>
        <Dialog.FooterCancelBtn
          onClick={() => onOpenChange(false)}
          data-tracking-id="builder-message-dialog-confirmation-cancel-button"
        >
          {t('generics.cancel')}
        </Dialog.FooterCancelBtn>
        <Dialog.FooterActionBtn
          onClick={() => {
            onConfirm();
            setIsEditing(false);
            onOpenChange(false);
          }}
          data-tracking-id="builder-message-dialog-confirmation-confirm-button"
        >
          {t('generics.confirm')}
        </Dialog.FooterActionBtn>
      </Dialog.Footer>
    </Dialog>
  ) : null;
};
