import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { Button, DropdownPanel, IconButton, Skeleton, useToast } from "@floqastinc/flow-ui_core";
import ArrowBack from "@floqastinc/flow-ui_icons/material/ArrowLeftAlt";
import Layers from "@floqastinc/flow-ui_icons/material/LayersOutlined";
import SearchActivity from "@floqastinc/flow-ui_icons/material/SearchActivity";
import Settings from "@floqastinc/flow-ui_icons/material/SettingsOutlined";
import { WorkflowStatus } from "@floqastinc/transform-v3";
import { useQuery } from "@tanstack/react-query";
import * as Styled from "./index.styles";
import { HeaderTitle } from "./components/HeaderTitle";
import { t } from "@/utils/i18n";
import { useExamples } from "@v3/examples";
import { ActivityLogSideDrawer } from "@Transform/components/ActivityLogSideDrawer/ActivityLogSideDrawer";
import { useUpdateWorkflow } from "@BuilderV3/routes/workflows/BuilderPage.hooks";
import { getWorkflowQuery } from "@BuilderV3/api/workflows";
import { useFeatureFlags } from "@/components/FeatureFlag";
import { SettingsSideDrawer } from "@Transform/components/SettingsSideDrawer/SettingsSideDrawer";
import { AGENTS } from "@/constants";

const Graph = ({ size = 24 }: { size?: number }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.17476 19.4902C2.43009 19.4902 1.79651 19.2304 1.27401 18.7108C0.751676 18.1909 0.490509 17.5598 0.490509 16.8173C0.490509 16.2121 0.673176 15.672 1.03851 15.197C1.40384 14.722 1.86534 14.407 2.42301 14.252V5.748C1.86534 5.593 1.40384 5.278 1.03851 4.803C0.673176 4.328 0.490509 3.78792 0.490509 3.18275C0.490509 2.43758 0.751092 1.80417 1.27226 1.2825C1.79359 0.760833 2.42668 0.5 3.17151 0.5C3.91618 0.5 4.54968 0.760833 5.07201 1.2825C5.59451 1.80417 5.85576 2.43758 5.85576 3.18275C5.85576 3.78792 5.67468 4.328 5.31251 4.803C4.95034 5.278 4.48718 5.593 3.92301 5.748V6.10575C3.92301 6.97925 4.22593 7.72167 4.83176 8.333C5.43759 8.94433 6.17318 9.25 7.03851 9.25H8.96151C10.2358 9.25 11.322 9.70192 12.22 10.6058C13.1182 11.5094 13.5673 12.6023 13.5673 13.8845V14.252C14.1314 14.407 14.5962 14.7204 14.9615 15.1923C15.3268 15.6641 15.5095 16.2026 15.5095 16.8078C15.5095 17.5529 15.2467 18.1863 14.721 18.708C14.1955 19.2295 13.5613 19.4902 12.8185 19.4902C12.0757 19.4902 11.4443 19.2295 10.9243 18.708C10.4043 18.1863 10.1443 17.5529 10.1443 16.8078C10.1443 16.2026 10.3253 15.6641 10.6875 15.1923C11.0497 14.7204 11.5096 14.407 12.0673 14.252V13.8845C12.0673 13.0192 11.7653 12.2804 11.1615 11.6683C10.5575 11.0561 9.82418 10.75 8.96151 10.75H7.03851C6.42051 10.75 5.84618 10.6337 5.31551 10.401C4.78468 10.1683 4.32051 9.852 3.92301 9.452V14.252C4.48718 14.407 4.95034 14.7204 5.31251 15.1923C5.67468 15.6641 5.85576 16.2026 5.85576 16.8078C5.85576 17.5529 5.59509 18.1863 5.07376 18.708C4.55243 19.2295 3.91943 19.4902 3.17476 19.4902ZM3.17801 18C3.50934 18 3.78851 17.8867 4.01551 17.66C4.24234 17.4335 4.35576 17.1528 4.35576 16.8178C4.35576 16.4828 4.24243 16.2018 4.01576 15.975C3.78909 15.748 3.50818 15.6345 3.17301 15.6345C2.84484 15.6345 2.56568 15.7496 2.33551 15.9798C2.10551 16.2099 1.99051 16.4891 1.99051 16.8173C1.99051 17.1454 2.10551 17.4246 2.33551 17.6548C2.56568 17.8849 2.84651 18 3.17801 18ZM12.822 18C13.1535 18 13.4327 17.8867 13.6595 17.66C13.8865 17.4335 14 17.1528 14 16.8178C14 16.4828 13.8867 16.2018 13.66 15.975C13.4333 15.748 13.1524 15.6345 12.8173 15.6345C12.4891 15.6345 12.2099 15.7496 11.9798 15.9798C11.7496 16.2099 11.6345 16.4891 11.6345 16.8173C11.6345 17.1454 11.7496 17.4246 11.9798 17.6548C12.2099 17.8849 12.4907 18 12.822 18ZM3.17801 4.3655C3.50934 4.3655 3.78851 4.25217 4.01551 4.0255C4.24234 3.799 4.35576 3.51825 4.35576 3.18325C4.35576 2.84825 4.24243 2.56733 4.01576 2.3405C3.78909 2.1135 3.50818 2 3.17301 2C2.84484 2 2.56568 2.11508 2.33551 2.34525C2.10551 2.57542 1.99051 2.85458 1.99051 3.18275C1.99051 3.51092 2.10551 3.79008 2.33551 4.02025C2.56568 4.25042 2.84651 4.3655 3.17801 4.3655Z"
        fill="#6B7280"
      />
    </svg>
  );
};

export const VersionSelectorButton = () => {
  const { workflowId = "", taskId = "" } = useParams();
  const examplesQuery = useExamples(
    {
      workflowId,
      taskId,
    },
    {
      enabled: !!workflowId && !!taskId,
    },
  );

  return (
    <Styled.VersionSelectorButton
      variant="outlined"
      color="dark"
      size="md"
      onClick={() => {
        console.log("TODO: Implement workflow versioning");
      }}
    >
      <Styled.VersionSelector>
        <Layers height={24} width={24} color="var(--flo-sem-color-icon-tertiary)" />
        <Styled.VersionIdentifier>
          {examplesQuery.isPending ? (
            <Skeleton variant="rectangle" width={24} />
          ) : (
            `V${(examplesQuery.data?.length ?? 0) + 1}`
          )}
        </Styled.VersionIdentifier>
      </Styled.VersionSelector>
    </Styled.VersionSelectorButton>
  );
};

export const Header = () => {
  const { showToast, Toast } = useToast();
  const { workflowId = "" } = useParams();
  const navigate = useNavigate();

  const { getFlag } = useFeatureFlags();

  const [showActivityLog, setShowActivityLog] = useState(false);
  const [isDropDownPanelOpen, setDropDownPanelOpen] = useState(false);
  const updateWorkflow = useUpdateWorkflow();
  const { data: workflow } = useQuery(getWorkflowQuery(workflowId));
  const [optimisticStatus, setOptimisticStatus] = useState<WorkflowStatus | null>(null);
  const displayStatus = optimisticStatus || workflow?.status || "DRAFT";

  const [showSettings, setShowSettings] = useState(false);

  const handleCloseSettings = () => {
    setShowSettings(false);
  };

  const handleOnChange = async (value: WorkflowStatus) => {
    setDropDownPanelOpen(false);
    if (!workflow) {
      return;
    }
    if (value !== displayStatus) {
      setOptimisticStatus(value);

      try {
        const updatedWorkflow = { ...workflow, status: value };
        await updateWorkflow.mutateAsync(updatedWorkflow);
      } catch (error) {
        setOptimisticStatus(workflow?.status || "DRAFT");
        console.error(
          t("components.Builder.Errors.errorUpdatingWorkflow") + `${workflow?.id}:`,
          error,
        );
        showToast(
          <Toast type="error">
            <Toast.Title>{t("components.Builder.Errors.errorUpdatingWorkflow")}</Toast.Title>
            <Toast.Message>
              {error instanceof Error
                ? error.message
                : t("components.Builder.Errors.errorUpdatingWorkflow")}
            </Toast.Message>
          </Toast>,
          { position: "bottom-right" },
        );
      }
    }
  };

  const renderLeft = () => {
    if (!workflow) {
      return <Skeleton variant="text" width="30%" height="100%" lineHeight={16} />;
    }
    return (
      <>
        <IconButton
          onClick={() => {
            navigate("/");
          }}
        >
          <ArrowBack color="var(--flo-base-color-neutral-900) !important" />
        </IconButton>
        <HeaderTitle workflowId={workflowId} />
      </>
    );
  };

  const renderRight = () => {
    if (!workflow) {
      return (
        <Skeleton
          variant="text"
          width="40%"
          height="100%"
          lineHeight={16}
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            marginLeft: "auto",
          }}
        />
      );
    }
    return (
      <>
        {getFlag("enable-canvas-view") ? (
          <Styled.HeaderIconButton variant="outlined" color="dark" size="md">
            <Graph size={24} />
          </Styled.HeaderIconButton>
        ) : null}
        <Styled.HeaderIconButton
          variant="outlined"
          color="dark"
          size="md"
          disabled={!workflow?.id}
          onClick={() => setShowActivityLog(true)}
        >
          <SearchActivity height={24} width={24} color="var(--flo-sem-color-icon-tertiary)" />
        </Styled.HeaderIconButton>
        {getFlag("enable-workflow-versioning") ? <VersionSelectorButton /> : null}
        {getFlag("enable-builder-page-settings") ? (
          <Styled.HeaderIconButton variant="outlined" color="dark" size="md">
            <Settings
              height={24}
              width={24}
              color="var(--flo-sem-color-icon-tertiary)"
              onClick={() => setShowSettings(true)}
            />
          </Styled.HeaderIconButton>
        ) : null}
        {getFlag("enable-builder-test-runs") ? (
          <Button
            variant="outlined"
            color="dark"
            onClick={() => {
              navigate(`/runner/v3/${AGENTS}/${workflowId}?runType=TEST&run=true`);
            }}
          >
            {t("components.Builder.testRun")}
          </Button>
        ) : null}
        <DropdownPanel
          isOpen={isDropDownPanelOpen}
          onOpenChange={setDropDownPanelOpen}
          onChange={handleOnChange}
          selectedValues={workflow?.status}
          selectionMode="single"
          data-tracking-id="builder-agent-status-dropdown"
          disableFilter={true}
          disableClear={true}
          style={{
            zIndex: 2,
          }}
        >
          <DropdownPanel.Trigger>
            <Styled.DropdownButton
              open={isDropDownPanelOpen}
              data-testid="builder-agent-status-dropdown"
            >
              {`${displayStatus?.[0]}${displayStatus?.slice(1, displayStatus?.length).toLowerCase()}`}
            </Styled.DropdownButton>
          </DropdownPanel.Trigger>
          <DropdownPanel.Content size="trigger-width" style={{ zIndex: 2 }}>
            {workflow?.humanInTheLoop ? (
              <DropdownPanel.Option value="ACTIVE">{t("generics.active")}</DropdownPanel.Option>
            ) : (
              <DropdownPanel.Option
                value="ACTIVE"
                isDisabled={true}
                tooltipProps={{
                  contentText: t("components.Runner.activeAgentsRequire"),
                  contentProps: {
                    hasArrow: false,
                    style: {
                      whiteSpace: "normal",
                      maxWidth: "150px",
                      wordBreak: "break-word",
                      zIndex: 2,
                    },
                  },
                }}
                style={{
                  fontWeight: 400,
                  color: "var(--flo-base-color-neutral-400)",
                }}
              >
                {t("generics.active")}
              </DropdownPanel.Option>
            )}
            <DropdownPanel.Option value="DRAFT">{t("generics.draft")}</DropdownPanel.Option>
            <DropdownPanel.Option value="ARCHIVED">{t("generics.archived")}</DropdownPanel.Option>
          </DropdownPanel.Content>
        </DropdownPanel>
      </>
    );
  };

  return (
    <>
      <Styled.Header>
        <Styled.HeaderLeft>{renderLeft()}</Styled.HeaderLeft>
        <Styled.HeaderRight>{renderRight()}</Styled.HeaderRight>
      </Styled.Header>
      <ActivityLogSideDrawer
        show={showActivityLog}
        agentId={workflowId}
        agentName={workflow?.name || ""}
        onClose={() => setShowActivityLog(false)}
      />
      <SettingsSideDrawer
        workflowId={workflowId}
        show={showSettings}
        onClose={handleCloseSettings}
      />
    </>
  );
};
