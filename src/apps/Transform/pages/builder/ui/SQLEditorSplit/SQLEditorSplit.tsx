import { useEffect, useMemo, useRef, useState } from 'react';
import Markdown from 'react-markdown';
import { useParams } from 'react-router';
import { match } from 'ts-pattern';
import { sql } from '@codemirror/lang-sql';
import { Compartment } from '@codemirror/state';
import {
  Button,
  ButtonGroup,
  Skeleton,
  Spinner,
  Text,
  TextArea,
  useToast,
} from '@floqastinc/flow-ui_core';
import ContentyCopyOutlined from '@floqastinc/flow-ui_icons/material/ContentCopyOutlined';
import PlayCircleOutlined from '@floqastinc/flow-ui_icons/material/PlayCircleOutlined';
import { useMutation, useQuery } from '@tanstack/react-query';
import { basicSetup, EditorView } from 'codemirror';
import { Parser } from 'node-sql-parser';
import { TooltipIconButton } from '../Chat/components/TooltipIconButton';
import * as Styled from './styled';
import { t } from '@/utils/i18n';
import { queryClient } from '@/components';
import { Li } from '@/components/Li';
import { FlolakeConnectionData } from '@/api/shared/types';
import { v3 } from '@/services/v3';
import { ArrowWarmUpRun } from '@/svg/ArrowWarmUpRun';
import { getExampleOutputsQuery } from '@BuilderV3/api/example-outputs';
import { getExampleQuery } from '@BuilderV3/api/examples';
import { queryKeys } from '@BuilderV3/api/query-keys';
import { getWorkflowTaskQuery } from '@BuilderV3/api/tasks';
import { convertSchemaToToken, convertTokenToSchema } from '@BuilderV3/utils/conversionFunctions';
import { createSchemaReferenceExtensions } from '@BuilderV3/utils/schemaReferenceExtensions';
import {
  hasTooltipWarning,
  scanQueryForWarnings,
  tooltipErrorMessage,
} from '@BuilderV3/utils/tooltip';
import { ConnectionsProvider } from '@Transform/pages/connections/ConnectionsProvider';

type SQLEditorSplitProps = {
  connections: FlolakeConnectionData[];
  editor: EditorView | null;
  setEditor: React.Dispatch<React.SetStateAction<EditorView | null>>;
  isFlolakeDataLoading: boolean;
  setIsSQLFileLoading: (isLoading: boolean) => void;
};

type Message = {
  role: string;
  content: string | undefined;
  status?: 'success' | 'error';
  errorMessage?: string;
};

export const SQLEditorSplit = ({
  setIsSQLFileLoading,
  editor,
  setEditor,
  connections,
  isFlolakeDataLoading,
}: SQLEditorSplitProps) => {
  const { workflowId = '', taskId = '', exampleSetId = '' } = useParams();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [currentQuery, setCurrentQuery] = useState<string>('');
  const [isQueryConverting, setIsQueryConverting] = useState<boolean>(false);
  const [activeState, setActiveState] = useState({ chat: false, sql: true });
  const [chatMessage, setChatMessage] = useState('');
  const containerRef = useRef<HTMLDivElement | null>(null);
  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  const [isAtBottom, setIsAtBottom] = useState(true);
  const [messages, setMessages] = useState<Message[]>([]);

  const schemaExtensionsRef = useRef(new Compartment());

  const { showToast, Toast } = useToast();
  const sqlParser = useMemo(() => new Parser(), []);
  const taskQuery = useQuery(getWorkflowTaskQuery({ workflowId, taskId }));
  const currentTask = taskQuery.data;
  const taskStrategy = currentTask?.strategy;

  const exampleOutputsQuery = useQuery({
    ...getExampleOutputsQuery({ workflowId, taskId, exampleSetId }),
    enabled: !!workflowId && !!taskId && !!exampleSetId,
  });

  useEffect(() => {
    if (isAtBottom && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isAtBottom]);
  const exampleSetQuery = useQuery({
    ...getExampleQuery({
      workflowId,
      taskId,
      exampleSetId,
    }),
    enabled: !!workflowId && !!taskId && !!exampleSetId,
  });

  const exampleStrategyData = exampleSetQuery.data;
  const exampleStrategy = exampleSetQuery.data?.strategy;
  const isDraft = exampleSetQuery.data?.status === 'DRAFT';

  useEffect(() => {
    const view = new EditorView({
      doc: '',
      extensions: [
        basicSetup,
        EditorView.lineWrapping,
        EditorView.lineWrapping,
        EditorView.theme({
          '&': {
            fontSize: '14px',
          },
          '.cm-content': {
            fontFamily: 'monospace',
            lineHeight: '1.6',
          },
        }),
        isDraft && !isQueryConverting
          ? EditorView.editable.of(true)
          : EditorView.editable.of(false),
        EditorView.updateListener.of((update) => {
          if (update.docChanged) {
            const query = update.state.doc.toString();
            setCurrentQuery(query);
          }
        }),
        sql({ upperCaseKeywords: true }),
        schemaExtensionsRef.current.of([]),
      ],
      parent: document.querySelector('#editor') as HTMLElement,
    });

    setEditor(view);

    return () => {
      view.destroy();
    };
  }, [isDraft, setCurrentQuery, isQueryConverting, taskStrategy, setEditor]);

  useEffect(() => {
    if (editor && connections) {
      try {
        editor.dispatch({
          effects: schemaExtensionsRef.current.reconfigure(
            createSchemaReferenceExtensions(connections),
          ),
        });
      } catch (error) {
        console.error('Error applying schema:', error);
      }
    }
  }, [editor, connections]);

  useEffect(() => {
    if (exampleStrategy?.kind === 'FLOLAKE') {
      // Determine which statement to use - fall back to taskStrategy if exampleStrategy statement is empty
      const statement =
        exampleStrategy.statement ||
        (taskStrategy?.kind === 'FLOLAKE' ? taskStrategy.statement : '');

      if (statement) {
        setIsQueryConverting(true);
      }

      if (statement && !isFlolakeDataLoading) {
        setIsQueryConverting(false);
      }

      if (statement && editor && !isFlolakeDataLoading) {
        const tokenizedQuery = convertSchemaToToken(statement, connections);

        editor.dispatch({
          changes: {
            from: 0,
            to: editor.state.doc.length,
            insert: tokenizedQuery,
          },
        });
        editor.focus();
      }
    }
  }, [taskStrategy, editor, isFlolakeDataLoading, isDraft, exampleStrategy]);

  const validateSQL = (sql: string): boolean => {
    try {
      // TODO: dc - this is a hack to replace the bindmapping with dummy values so that the sql parser can parse it
      const sqlWithDummyValues = sql.replace(/:\d+/g, '1');
      sqlParser.astify(sqlWithDummyValues, {
        database: 'snowflake',
      });
      return true;
    } catch {
      return false;
    }
  };

  function showAppToast(type: 'success' | 'error', title: string, message: string) {
    showToast(
      <Toast type={type}>
        <Toast.Title>{title}</Toast.Title>
        <Toast.Message>{message}</Toast.Message>
      </Toast>,
      { position: 'top-right' },
    );
  }

  const extractSourcesFromQuery = (sqlQuery: string): string[] => {
    // Look for TLC format with underscores and extract the source name
    // Example: TLC_66CA67A8F9B2F84CB05E2791_5T_67C1F3BE664390427180208A_ERP_COUPA.ACCOUNT_TYPE
    const sourceRegex = /TLC_[A-Z0-9]+_[A-Z0-9]+_[A-Z0-9]+_ERP_([A-Z]+)\./gi;
    const sources = new Set<string>();

    let match;
    while ((match = sourceRegex.exec(sqlQuery)) !== null) {
      // eslint-disable-next-line no-restricted-syntax
      const sourceName = match[1].toUpperCase();
      sources.add(sourceName);
    }

    return Array.from(sources);
  };

  // Mutations
  const updateExampleMutation = useMutation({
    mutationFn: async ({ query }: { query: string }) => {
      if (!exampleStrategyData) {
        throw new Error('Current task not found');
      }

      const sources = extractSourcesFromQuery(query);

      const updatedExample = {
        name: exampleStrategyData.name,
        status: exampleStrategyData.status,
        strategy: {
          kind: 'FLOLAKE' as const,
          statement: query,
          bindMapping: [],
          sources: sources as any,
        },
      };

      return v3.examples.updateExample({
        workflowId,
        taskId,
        exampleSetId,
        example: updatedExample,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.workflows.byId(workflowId),
      });
    },
    onError: (data) => {
      console.error('Error updating task:', data);
    },
  });

  const executeFlolakeSQL = useMutation({
    mutationFn: async ({ exampleOutputId }: { exampleOutputId: string }) => {
      const response = await v3.flolakeExecuteService.executeFlolakeSQL({
        workflowId,
        taskId,
        exampleSetId,
        exampleOutputId: exampleOutputId,
      });

      if (response?.errors?.length) {
        console.error(response.errors);
        throw new Error('Network response was not ok');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.taskOutputs.byTask({ workflowId, taskId }),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.exampleOutputs.getExampleOutputs({
          workflowId,
          taskId,
          exampleSetId,
        }),
      });
      queryClient.invalidateQueries({
        queryKey: [
          {
            resource: 'exampleOutputs',
            params: { workflowId, taskId, exampleSetId },
          },
        ],
      });
    },
    onError: (error) => {
      showAppToast(
        'error',
        t('components.Builder.taskRunFailed'),
        t('components.Builder.pleaseCheckQuery'),
      );
      console.error('Error executing workflow:', error);
    },
  });

  const handleExecute = async (query?: string, generated?: boolean) => {
    try {
      setIsLoading(true);
      setIsSQLFileLoading(true);

      const queryToUse = query || currentQuery;
      scanQueryForWarnings(queryToUse, connections);

      if (hasTooltipWarning) {
        showAppToast(
          'error',
          t('components.Builder.Errors.invalidConnection'),
          tooltipErrorMessage || t('components.Builder.checkSQLSyntax'),
        );
        setIsLoading(false);
        setIsSQLFileLoading(false);
        return;
      }

      let convertedQuery = queryToUse;
      if (!generated) {
        convertedQuery = convertTokenToSchema(queryToUse, connections);
      }

      if (!convertedQuery) {
        showAppToast(
          'error',
          t('components.Builder.Errors.noConvertedSQL'),
          t('components.Builder.Errors.noConvertedSQLMessage'),
        );
        setIsLoading(false);
        setIsSQLFileLoading(false);
        return false;
      }

      if (!validateSQL(convertedQuery)) {
        showAppToast(
          'error',
          t('components.Builder.invalidSQLQuery'),
          t('components.Builder.checkSQLSyntax'),
        );
        setIsLoading(false);
        setIsSQLFileLoading(false);
        return false;
      }

      await updateExampleMutation.mutateAsync({ query: convertedQuery });

      const exampleOutputId = exampleOutputsQuery.data?.[0]?.id;

      if (!exampleOutputId) {
        throw new Error('No example output id found');
      }

      await executeFlolakeSQL.mutateAsync({ exampleOutputId });

      showAppToast(
        'success',
        t('components.Builder.taskRunSuccess'),
        t('components.Builder.taskBeenRunSuccessfully'),
      );
      setIsLoading(false);
      setIsSQLFileLoading(false);
    } catch (error) {
      console.error('Error executing workflow:', error);
      setIsLoading(false);
      setIsSQLFileLoading(false);
    }
  };

  const nlToSqlMutation = useMutation({
    mutationFn: async (prompt: string) => {
      const response = await v3.nlToSqlService.nlSql({
        nlsql: { naturalLanguageQuery: prompt },
      });
      if (!response || response.errors?.length) {
        throw new Error('Failed to generate SQL');
      }
      return response.data?.sql;
    },
    onSuccess: (sql) => {
      showAppToast(
        'success',
        t('components.Builder.sqlGenerated'),
        t('components.Builder.sqlGeneratedSuccess'),
      );
      setMessages((prevMessages) => [
        ...prevMessages,
        { role: 'assistant', content: sql, status: 'success' },
      ]);
    },
    onError: (error) => {
      showAppToast(
        'error',
        t('components.Builder.Errors.generationFailed'),
        t('components.Builder.Errors.generationFailedMessage'),
      );
      setMessages((prev) => [
        ...prev,
        {
          role: 'assistant',
          content: 'Sorry, I could not generate a valid SQL for your request.',
          errorMessage: error.message,
          status: 'error',
        },
      ]);
    },
  });

  const handleScroll = () => {
    if (containerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
      // Consider "at bottom" if within 100px of the bottom
      const atBottom = scrollHeight - scrollTop - clientHeight < 100;
      setIsAtBottom(atBottom);
    }
  };

  return (
    <ConnectionsProvider>
      <Styled.SQLEditorContainer>
        <Styled.SQLEditorHeader>
          <Text weight={4} size={5} lineHeight={8} style={{ paddingLeft: '10px' }}>
            {t('generics.editor')}
          </Text>
          <Styled.SQLEditorActionsContainer>
            <ButtonGroup style={{ padding: '12px 8px 12px 0px' }}>
              <ButtonGroup.Button
                onClick={() => setActiveState({ chat: true, sql: false })}
                style={{ height: '32px' }}
                isActive={activeState.chat}
              >
                <Text style={{}}>{t('generics.chat')}</Text>
              </ButtonGroup.Button>
              <ButtonGroup.Button
                isActive={activeState.sql}
                onClick={() => setActiveState({ chat: false, sql: true })}
                style={{ height: '32px', width: '100%' }}
              >
                <Text style={{}}>{t('components.Builder.sqlEditor')}</Text>
              </ButtonGroup.Button>
            </ButtonGroup>
            <Button
              style={{ height: '32px', width: '92px' }}
              onClick={() => handleExecute()}
              disabled={isLoading || !isDraft}
            >
              {t('generics.generate')}
            </Button>
          </Styled.SQLEditorActionsContainer>
        </Styled.SQLEditorHeader>

        <Styled.SQLEditorWrapper
          id="editor"
          style={{ display: activeState.sql ? 'block' : 'none' }}
        />

        {/* Can possibly be pulled out into separate component */}
        {activeState.chat && (
          <Styled.ChatContent>
            <Styled.ChatLog ref={containerRef} onScroll={handleScroll}>
              <Styled.ChatLogMessages>
                {messages.map((message, index) => {
                  return (
                    match(message)
                      // eslint-disable-next-line i18next/no-literal-string
                      .with({ role: 'prompt' }, (msg) => (
                        <Styled.UserChatContainer key={index}>
                          <Styled.UserChat>
                            {message.content && (
                              <>
                                <Markdown
                                  components={{
                                    li({ node: _node, ...rest }) {
                                      return <Li {...rest} />;
                                    },
                                  }}
                                >
                                  {message.content}
                                </Markdown>
                                <TooltipIconButton
                                  title={t('components.Builder.copyUserPrompt')}
                                  tooltipMessage={t('components.Builder.copyUserPrompt')}
                                  icon={<ContentyCopyOutlined />}
                                  onClick={() => navigator.clipboard.writeText(msg.content ?? '')}
                                />
                              </>
                            )}
                          </Styled.UserChat>
                        </Styled.UserChatContainer>
                      ))
                      // eslint-disable-next-line i18next/no-literal-string
                      .with({ role: 'assistant' }, (msg) => (
                        <Styled.AssistantChat key={index}>
                          <Markdown
                            components={{
                              li({ node: _node, ...rest }) {
                                return <Li {...rest} />;
                              },
                            }}
                          >
                            {msg.content}
                          </Markdown>
                          {msg.status === 'success' && (
                            <div
                              style={{
                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'center',
                                gap: 8,
                                marginTop: 8,
                              }}
                            >
                              <TooltipIconButton
                                title={t('components.Builder.copySQL')}
                                tooltipMessage={t('components.Builder.copySQL')}
                                icon={<ContentyCopyOutlined />}
                                onClick={() => navigator.clipboard.writeText(msg.content ?? '')}
                              />

                              <TooltipIconButton
                                title={t('components.Builder.executeLLMGeneratedSQL')}
                                tooltipMessage={t('components.Builder.executeLLMGeneratedSQL')}
                                icon={<PlayCircleOutlined />}
                                onClick={() => handleExecute(msg.content, true)}
                              />
                            </div>
                          )}

                          {msg.status === 'error' && msg.errorMessage && (
                            <div
                              style={{
                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'center',
                                gap: 8,
                                marginTop: 8,
                              }}
                            >
                              <TooltipIconButton
                                title={t('components.Builder.copySQLErrorMessage')}
                                tooltipMessage={t('components.Builder.copySQLErrorMessage')}
                                icon={<ContentyCopyOutlined />}
                                onClick={() =>
                                  navigator.clipboard.writeText(msg.errorMessage ?? '')
                                }
                              />
                            </div>
                          )}
                        </Styled.AssistantChat>
                      ))
                      .otherwise(() => null)
                  );
                })}
                {nlToSqlMutation.isPending && (
                  <Styled.AssistantChat>
                    <div style={{ display: 'flex', flexDirection: 'row', gap: '2px' }}>
                      <Skeleton variant="circle" radius="4" />
                      <Skeleton variant="circle" radius="4" />
                      <Skeleton variant="circle" radius="4" />
                    </div>
                  </Styled.AssistantChat>
                )}
                <div ref={messagesEndRef} style={{ height: '0' }}></div>
              </Styled.ChatLogMessages>
              {isDraft && (
                <Styled.ChatBox>
                  <TextArea
                    aria-label={t('components.Builder.chatMSGTextArea')}
                    placeholder={t('components.Builder.typeCommand')}
                    value={chatMessage}
                    onChange={setChatMessage}
                    disabled={nlToSqlMutation.isPending || isLoading}
                    styleOverrides={{
                      textarea: {
                        width: '100%',
                        height: '60px',
                        minHeight: '60px',
                        borderBottomRightRadius: '0',
                        borderBottomLeftRadius: '0',
                        borderTopRightRadius: '8px',
                        borderTopLeftRadius: '8px',
                        border: '1px solid var(--flo-sem-color-border)',
                        borderBottom: 'none',
                        outline: 'none',
                        boxShadow: 'none',
                      },
                    }}
                  />

                  <Styled.TextAreaActionBar>
                    {/* For future add button */}
                    <div></div>
                    <Styled.SubmitChatButton
                      onClick={() => {
                        if (chatMessage.trim()) {
                          setChatMessage('');
                          setMessages((prevMessages) => [
                            ...prevMessages,
                            { role: 'prompt', content: chatMessage },
                          ]);
                          nlToSqlMutation.mutate(chatMessage);
                        }
                      }}
                      disabled={!chatMessage.trim() || nlToSqlMutation.isPending}
                      data-testid="submit-chat-button"
                    >
                      {nlToSqlMutation.isPending ? <Spinner /> : <ArrowWarmUpRun />}
                    </Styled.SubmitChatButton>
                  </Styled.TextAreaActionBar>
                </Styled.ChatBox>
              )}
            </Styled.ChatLog>
          </Styled.ChatContent>
        )}
      </Styled.SQLEditorContainer>
    </ConnectionsProvider>
  );
};
