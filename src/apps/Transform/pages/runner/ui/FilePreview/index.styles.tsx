import { styled } from 'styled-components';
import { FilePreviewMode } from '.';

export const EmptyFilePreview = styled.div`
  display: flex;
  height: 100%;
  width: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
`;

export const FilePreviewPanel = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-items: center;
  height: 100%;
  width: 100%;
  overflow: hidden;
`;

export const FilePreviewHeader = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px 0 8px;
  width: 100%;
`;

export const FilePreviewHeaderLeft = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
  gap: 8px;
  overflow: hidden;
  height: 100%;
`;

export const FilePreviewMetaData = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
`;

export const StepName = styled.div<{ mode: FilePreviewMode }>`
  width: ${({ mode }) => (mode === 'builder' ? '5.75rem' : 'auto')};
`;

export const FilePreview = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  min-height: 0;
  gap: 20px;
  width: 100%;
`;

export const GridContainer = styled.div`
  flex: 1;
  height: 100%;
  overflow: hidden;
  min-height: 0;

  .ag-root-wrapper {
    border-radius: 0;
  }
  .ag-root {
    border-radius: 0;
  }
`;

export const GridSection = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  margin-right: 8px
  min-height: 0;
  overflow: hidden;
`;

export const SheetTabs = styled.div`
  background: #ededed;
  height: 36px;
  display: flex;
  overflow-x: auto;
  max-width: 100%;

  &::-webkit-scrollbar {
    background: #ededed;
  }

  scrollbar-width: thin;
`;

export const SheetTab = styled.button<{ active: boolean }>`
  padding: 8px 12px;
  border-width: 0 1px;
  border-style: solid;
  border-color: #dcdddd;
  cursor: default;
  display: flex;
  align-items: center;
  gap: 8px;

  margin-right: -1px;
  height: 100%;
  background: ${({ active }) => (active ? '#EDEDED' : 'var(--flo-base-color-neutral-0)')};

  &:hover {
    background: ${({ active }) => (!active ? 'var(--flo-base-color-neutral-0)' : '#EDEDED')};
  }
`;

export const FileTabs = styled.div`
  background: white;
  height: 100%;
  display: flex;
  overflow-x: auto;
  flex: 1;
  overflow-x: auto;
  width: 100%;
  padding-right: 1px;

  &::-webkit-scrollbar-thumb {
    background: #bbb;
    border-radius: 4px;
  }

  scrollbar-width: thin;
  -ms-overflow-style: -ms-autohiding-scrollbar;

  /* Hide scrollbar in most browsers but keep functionality */
  &::-webkit-scrollbar {
    height: 2px;
  }
`;

export const FileTab = styled.button<{ active: boolean; hasCloseButton?: boolean }>`
  padding: ${({ hasCloseButton }) =>
    hasCloseButton ? '8px 6px 8px 12px' : '8px 12px'}; /* Symmetric padding when no close button */
  border-width: 0 1px;
  border-style: solid;
  border-color: #dcdddd;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${({ hasCloseButton }) => (hasCloseButton ? '8px' : '0')}; /* No gap when no close button */
  height: 100%;

  margin-right: -1px;
  height: 100%;

  background: ${({ active }) => (active ? '#EDEDED' : 'var(--flo-base-color-neutral-0)')};

  &:hover {
    background: ${({ active }) => (!active ? 'var(--flo-base-color-neutral-0)' : '#EDEDED')};
  }
`;

export const DownloadButtonContainer = styled.div`
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  padding-left: 8px;
  border-left: 1px solid #dcdddd;
  height: 100%;
`;
