{"components": {"ActivityLog": {"activeAgentsRequire": "", "activityLog": "", "addInputsToRunAgent": "", "agentHasNoSteps": "", "agentHasNoSteps2": "", "agentPreview": "", "agentPreview2": "", "agentSteps": "", "approveRun": "", "enterInput": "", "followingStepsExecuted": "", "inputsCount": "", "journalEntries": "", "rejectRun": "", "resumeRun": "", "runAgent": "", "runBy": "", "stepOfInput": "", "unsupportedInputType": "", "uploadInput": "", "uploadInputFile": "", "viewErrorDetails": ""}, "Admin": {"actionPermanentDeleteTeam": "", "actionWillMoveUser": "", "actionWillMoveYou": "", "additionalFunctionality": "", "APIKeyCreated": "", "apiKeyDeleted": "", "APImakeRequests": "", "APISwaggerUI": "", "attemptedRuns": "", "backToApp": "", "changeTeam": "", "completedAttempted": "", "completionRate": "", "createdAt": "", "createdNewAPIKey": "", "createKey": "", "deleteAPIKey": "", "deleteTeam": "", "deleteUser": "", "emailedTempPassword": "", "emailSent": "", "failedToFetchScript": "", "failedToSaveScript": "", "filterWorkflows": "", "getStartedInviting": "", "hasBeenMigrated": "", "inviteByEmail": "", "inviteMoreUsers": "", "inviteSent": "", "inviteUser": "", "lastUsed": "", "loadMore": "", "newCreatedBy": "", "newEntityID": "", "noAPIKey": "", "noOneHere": "", "notFound": "", "noWorkflowsFound": "", "organizationSettings": "", "permanentlyDelete": "", "permanentlyDeleteAPIKey": "", "permDeleteUser": "", "pleaseCopySecret": "", "resendEmail": "", "roleUpdated": "", "runDetails": "", "runsByDate": "", "runsByStatus": "", "runStats": "", "scriptSaved": "", "secretAPIKey": "", "secretKey": "", "selectStep": "", "selectTeamMigrate": "", "sentInvitationTo": "", "somethingWentWrong": "", "startAPIKey": "", "switchTeams": "", "taskRunStats": "", "taskRunStatsByDate": "", "taskRunStatsByStrat": "", "teamExternalID": "", "teamName": "", "theWorkflow": "", "totalRuns": "", "typeDelete": "", "unableCreateAPI": "", "unableDeleteAPI": "", "unableDeleteUser": "", "unableTempPassword": "", "unableToInviteUser": "", "unableToMigrateWorkflow": "", "unableToUpdateUserRole": "", "userDeleted": "", "userDeletedMessage": "", "userDeleteUndone": "", "userRoleUpdated": "", "users": "", "viewScripts": "", "workflow": "", "workflowID": "", "workflowMigrated": "", "workflowRunID": "", "workflows:": "", "workflowScripts": "", "workflowStats": ""}, "AgentList": {"connectedData": "", "deleteAgent": "", "editAgent": "", "lastEdited": "", "lastRun": "", "oneHumanStep": "", "searchAgents": "", "showActivityLog": "", "unexpectedFallthrough": ""}, "Builder": {"aboutTo": "", "aboutTo2": "", "aboutToDeleteStep": "", "addCustomPrompt": "", "addInput": "", "addInputsPrevSteps": "", "addItem": "", "addNewStep": "", "addSection:": "", "agentDescriptionOptional": "", "agentName": "", "allFilesUnchanged": "", "analyzingFiles": "", "aPreviousStep": "", "archived": "", "areYouSure": "", "areYouSureRegenerateResponse": "", "builderInput": "", "cannotUndo": "", "changingFrom": "", "chatMSGTextArea": "", "chatWithAI": "", "checkSQLSyntax": "", "confirmAction": "", "couldTakeAMinute": "", "CreateAgent": {"advancedSetup": "", "modes": ""}, "createBlankVersion": "", "createStep": "", "createVersion": "", "currentOutput": "", "dataSources": "", "default": "", "deleteInput": "", "deleteMessage": "", "deleteRun": "", "deleteStep": "", "deleteStepAfter": "", "deleteVersion": "", "detailsHere": "", "developmentVer": "", "draft": "", "draftMessage": "", "dropFilesSendMsg": "", "edit": "", "editMessage": "", "editNotes": "", "editTaskDescription": "", "editTitle": "", "enhanceWithAI": "", "enhancingWithAI": "", "ensureInputsFilled": "", "enterADesc": "", "enterATitle": "", "enterSQLQuery": "", "Errors": {"agentNameRequired": "", "badNetworkResponse": "", "cantTransformSource": "", "currentTaskNotFound": "", "emptyMessage": "", "emptyPrompt": "", "enterSQLDescription": "", "errorApplyingSchema": "", "errorCreatingContext": "", "errorDuringCleanup": "", "errorExecutingFlolake": "", "errorExecutingWorkflow": "", "errorFetchingEntities": "", "errorHandlingInputs": "", "errorUpdatingTask": "", "failedCreateExample": "", "failedCreateInputs": "", "failedCreateOutputs": "", "failedCreateTask": "", "failedCreateTaskInput": "", "failedCreateTaskOutput": "", "failedCreateWorkflow": "", "failedDeleteExample": "", "failedDeleteInputs": "", "failedDeleteOutputs": "", "failedExampleInputs": "", "failedExampleOutputs": "", "failedGetExample": "", "failedGetTask": "", "failedGetTaskInputURI": "", "failedGetTaskOutputs": "", "failedGetWorkflowRuns": "", "failedInputFile": "", "failedOutputFile": "", "failedPrincipals": "", "failedSetExampleInput": "", "failedToFetchDesc": "", "failedToGetWorkflow": "", "failedUpdateExperimentAssignments": "", "failedUpdateTask": "", "failedUploadFile": "", "failedWorkflowIn": "", "failedWorkflowInputs": "", "fetchJEMTemplate": "", "fetchMutation": "", "fileFromURI": "", "fileTypeUnsupported": "", "forItem1": "", "forItem2": "", "generationFailed": "", "generationFailedMessage": "", "IDRequired": "", "inputNotFound": "", "invalidColumnReference": "", "invalidConfirm": "", "invalidEntryKey": "", "invalidSchemaReference": "", "invalidSystem": "", "invalidTable": "", "invalidTableReference": "", "issueDeletingInput": "", "itemNotFoundIndex": "", "noData": "", "noDataExample": "", "noDataMessage": "", "noDataReturned": "", "noDataReturnedCEI": "", "noDataReturnedCTI": "", "noDataReturnedCWI": "", "noDataReturnedSEIFV": "", "noExampleOutputID": "", "noExamplePropsVOD": "", "noFileinDropdown": "", "noIDsprovided": "", "noMessageID": "", "noMsgIDDel": "", "noMsgIDEdit": "", "noMsgIDRegen": "", "noPrevTaskOutputs": "", "noRunID": "", "noStrategyType": "", "noTaskDelete": "", "noTaskRevert": "", "noWorkflowID": "", "schemaError": "", "somethingWentWrong": "", "taskDataUndefined": "", "taskExampleIDMissing": "", "taskIDinput": "", "taskIDoutput": "", "taskNotDefined": "", "taskNotFound": "", "transformingSystemName": "", "unableToGetDescription": "", "unexpectedFileError": "", "unexpectedInternalError": "", "unexpectedWorkflowInput": "", "unsupportedInputType": "", "unsupportedProvider": "", "unsupportedStrat": "", "workflowDataUndefined": "", "workflowDescNotFound": "", "workflowInputUri": ""}, "fileContextDisabled": "", "fileType": "", "finalizedVersionDefault": "", "forWorkflowToRun": "", "inactiveExperiments": "", "includeSelectedRange": "", "incompleteInput": "", "invalidSQLQuery": "", "loading": "", "messageDeleted": "", "messageRegenerated": "", "nameAgent": "", "newItem": "", "newVersionName": "", "noActiveExampleFound": "", "noActiveVersions": "", "noConnectionsAvailable": "", "noDataAvailable": "", "noEntityAvailable": "", "noExamplesFound": "", "noSourcesAvailable": "", "noSQLQuery": "", "noTablesAvailable": "", "notFoundIn": "", "notInUse": "", "notSupportPreview": "", "notYetRun": "", "noWorkflowRunsCan": "", "open": "", "optionalDescription": "", "pCol": "", "permanentlyDelete1": "", "permanentlyDelete2": "", "permanentlyDeleteInput": "", "pleaseCheckQuery": "", "pleaseWaitStep": "", "pTable": "", "published": "", "reachToSupport": "", "readyToRun": "", "regenerateAssistantResponse": "", "rename": "", "renameVersion": "", "requestedFormat": "", "runAgain": "", "runRemoved1": "", "search": "", "searchConnections": "", "selectAgentType": "", "selectDate": "", "selectEntity": "", "selectFile": "", "selectOption": "", "selectSource": "", "selectStrat": "", "selectTable": "", "selectTool": "", "selectType": "", "selectVersion": "", "setActive": "", "sqlEditorPrompt": "", "sqlEditorPromptLabel": "", "sqlEditorToggleLabel": "", "sqlGenerated": "", "sqlGeneratedSuccess": "", "statusChange1": "", "statusChange2": "", "statusChange3": "", "statusChange4": "", "statusChange5": "", "statusChange6": "", "statusChange7": "", "taskBeenRunSuccessfully": "", "taskDescription": "", "taskRunFailed": "", "taskRunSuccess": "", "testRun": "", "textInput": "", "theRange": "", "thisVersionIsArchived": "", "thisVersionIsDraft": "", "thisVersionIsPublished": "", "treeViewTitle": "", "unableCreateNewStep": "", "updatedTaskDescription": "", "versionExecutedWorkflow": "", "versionsCanBeOTFS": "", "viewDetails": "", "viewRunHistory": "", "willRemoveAllStepsAfter": "", "workFlowRunTableSubtitle": ""}, "Connections": {"Actions": {"manage": "", "requestAccess": "", "setup": ""}, "Data": {"availableData": "", "noDataAvailable": ""}, "Errors": {"loadingPage": "", "pleaseTryAgain": ""}, "Header": {"searchPlaceholder": ""}, "Permissions": {"adminAccessRequired": ""}, "Status": {"connected": "", "notConnected": ""}, "title": ""}, "FileInput": {"dropToUpload": "", "formatList": "", "uploadFiles": ""}, "HeaderTitle": {"ariaLabels": {"cancelEditing": "", "editWorkflowName": "", "saveChanges": ""}, "errors": {"nameRequired": "", "nameTooLong": ""}, "toast": {"errorTitle": "", "invalidInputTitle": "", "updateAgentError": ""}, "tooltip": {"editName": ""}}, "Runner": {"activeAgentsRequire": "", "activityLog": "", "addInputsToRunAgent": "", "agentHasNoSteps": "", "agentHasNoSteps2": "", "agentPreview": "", "agentPreview2": "", "agentSteps": "", "allAgents": "", "approveRun": "", "downloadFile": "", "enterInput": "", "enterNumber": "", "enterText": "", "Errors": {"badNetworkResponse": "", "errorRunningWorkflow": "", "inputError": "", "noActiveExample": "", "noDataFromWorkflowRun": "", "noDataReturned": "", "noDataReturnedFromTaskRun": "", "noFileFoundForID": "", "noInputFoundForID": "", "noRunData": "", "noTaskDescription": "", "noWorkflowID": "", "requestError": ""}, "followingStepsExecuted": "", "generatingDescription": "", "inputsCount": "", "makeActive": "", "noDataPreview": "", "rejectRun": "", "resumeRun": "", "runAgent": "", "runBy": "", "taskUnpublished": "", "unpublishedStep": "", "unsupportedInputType": "", "uploadFile": "", "uploadInput": "", "viewErrorDetails": ""}}, "generics": {"actions": "", "active": "", "add": "", "addFlag": "", "archived": "", "back": "", "beta": "", "build": "", "cancel": "", "close": "", "completed": "", "completion": "", "confirm": "", "connect": "", "continue": "", "count": "", "create": "", "created": "", "createdBy": "", "custom": "", "dashboard": "", "date": "", "delete": "", "deletion": "", "description": "", "details": "", "download": "", "downloadFile": "", "draft": "", "email": "", "enterDescription": "", "enterTitle": "", "enterValue": "", "error": "", "experiments": "", "failed": "", "file": "", "finish": "", "found": "", "generate": "", "ID": "", "inProgress": "", "inputs": "", "inputType": "", "lastModified": "", "lastRun": "", "match": "", "message": "", "name": "", "no": "", "output": "", "preview": "", "proceed": "", "publish": "", "regenerate": "", "rejected": "", "results": "", "role": "", "run": "", "runBy": "", "save": "", "search": "", "select": "", "selected": "", "send": "", "startedAt": "", "status": "", "step": "", "strategy": "", "submit": "", "success": "", "task": "", "teams": "", "templates": "", "text": "", "this": "", "title": "", "to": "", "TODO": "", "total": "", "undo": "", "userStatus": "", "versions": "", "view": "", "you": ""}}