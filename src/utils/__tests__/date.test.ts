import { FQIntl } from '@floqastinc/fq-intl';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { formatDate } from '../date';

// Mock the module and expose internal mocks for test access
vi.mock('@floqastinc/fq-intl', () => {
  return {
    FQIntl: {
      DateTimeFormat: vi.fn(),
    },
  };
});

const mockedDateTimeFormat = vi.mocked(FQIntl.DateTimeFormat);
const mockFormat = vi.fn();

const mockFormatDate = (dateValue: string, expectedFormat: string) => {
  const mockInstance = {
    format: mockFormat.mockImplementation(() => expectedFormat),
    formatToParts: vi.fn(),
    resolvedOptions: vi.fn(),
    resolvedIsoOptions: vi.fn(),
    formatRange: vi.fn(),
    formatRangeToParts: vi.fn(),
  } as unknown as InstanceType<typeof FQIntl.DateTimeFormat>;

  mockedDateTimeFormat.mockImplementation(() => mockInstance);

  const result = formatDate(dateValue);
  return {
    result,
    mockedDateTimeFormat,
    mockFormat,
  };
};

describe('formatDate', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('When given a date string', () => {
    it('should format a UTC date string correctly', () => {
      const ISODateString = '2025-06-03T00:00:00.000Z';
      const { result, mockedDateTimeFormat, mockFormat } = mockFormatDate(
        ISODateString,
        '6/3/2025',
      );
      expect(result).toBe('6/3/2025');

      expect(mockedDateTimeFormat).toHaveBeenCalledWith({ dateStyle: 'short' });
      expect(mockFormat).toHaveBeenCalled();

      const dateArg = mockFormat.mock.calls[0][0];
      expect(dateArg).toBeInstanceOf(Date);
      expect(dateArg.toISOString()).toBe(ISODateString);
    });

    it('should handle dates before 1970', () => {
      const ISODateString = '1969-12-20T00:00:00.000Z';
      const { result, mockFormat } = mockFormatDate(ISODateString, '12/20/1969');
      expect(result).toBe('12/20/1969');

      const dateArg = mockFormat.mock.calls[0][0];
      expect(dateArg.toISOString()).toBe(ISODateString);
    });

    it('should handle dates after 2038', () => {
      const ISODateString = '2050-01-01T00:00:00.000Z';
      const { result, mockFormat } = mockFormatDate(ISODateString, '1/1/2050');
      expect(result).toBe('1/1/2050');

      const dateArg = mockFormat.mock.calls[0][0];
      expect(dateArg.toISOString()).toBe(ISODateString);
    });
  });

  describe('When given a Date object', () => {
    it('should format a Date object correctly', () => {
      const date = new Date('2025-12-25T00:00:00.000Z');
      const { result, mockFormat } = mockFormatDate(date.toISOString(), '12/25/2025');
      expect(result).toBe('12/25/2025');

      const dateArg = mockFormat.mock.calls[0][0];
      expect(dateArg.toISOString()).toBe(date.toISOString());
    });

    it('should handle dates with time components correctly', () => {
      const date = new Date('2025-06-03T23:59:59.999Z');
      const { result, mockFormat } = mockFormatDate(date.toISOString(), '6/3/2025');
      expect(result).toBe('6/3/2025');

      const dateArg = mockFormat.mock.calls[0][0];
      expect(dateArg.toISOString()).toBe(date.toISOString());
    });
  });

  describe('When given a timestamp number', () => {
    it('should format a timestamp correctly', () => {
      const timestamp = 1735689600000; // 2025-01-01T00:00:00.000Z
      const date = new Date(timestamp);
      const { result, mockFormat } = mockFormatDate(date.toISOString(), '1/1/2025');
      expect(result).toBe('1/1/2025');

      const dateArg = mockFormat.mock.calls[0][0];
      expect(dateArg.getTime()).toBe(timestamp);
    });

    it('should handle negative timestamps (pre-1970)', () => {
      const timestamp = -1000; // 1969-12-31T23:59:59.000Z
      const date = new Date(timestamp);
      const { result, mockFormat } = mockFormatDate(date.toISOString(), '12/31/1969');
      expect(result).toBe('12/31/1969');

      const dateArg = mockFormat.mock.calls[0][0];
      expect(dateArg.getTime()).toBe(timestamp);
    });
  });

  describe('When given an invalid date', () => {
    it('should return "Invalid Date"', () => {
      const { result } = mockFormatDate('not-a-valid-date', 'Invalid Date');
      expect(result).toBe('Invalid Date');
    });
  });
});
