import { FQIntl } from '@floqastinc/fq-intl';

export const formatTaskDate = (taskDateStr: string | number | Date) => {
  const taskDate = new Date(taskDateStr);
  const dateFormatter = new FQIntl.DateTimeFormat({ dateStyle: 'short' });
  const date = dateFormatter.format(taskDate);

  const timeFormatter = new FQIntl.DateTimeFormat({ timeStyle: 'short' });
  const time = timeFormatter.format(taskDate);

  return `${date} at ${time}`;
};

/**
 * Formats an ISO date string (in UTC) to "M/D/YYYY" format without applying local timezone shifts.
 *
 * This is useful when displaying dates that should remain consistent across all timezones,
 * such as dates selected from a date picker or stored in a database without time context.
 *
 * #### Why this is needed
 *
 * JavaScript's `toLocaleDateString()` formats the date using the local timezone, which can cause
 * bugs such as off-by-one-day errors—especially when the date is stored as midnight UTC (e.g., "2025-06-03T00:00:00.000Z")
 * and viewed in a negative timezone like PST/PDT.
 *
 * By using the UTC-based getters (`getUTCMonth`, `getUTCDate`, and `getUTCFullYear`), we avoid unintended
 * timezone conversion and preserve the intended calendar date.
 */
export const formatDate = (dateStr: string | number | Date) => {
  const date = new Date(dateStr);

  if (isNaN(date.getTime())) {
    return 'Invalid Date';
  }

  const dateFormatter = new FQIntl.DateTimeFormat({ dateStyle: 'short' });
  return dateFormatter.format(date);
};
