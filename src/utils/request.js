import authState from "./authState";

export function getLambdaEndpoint(lambda, appTld = false) {
  const apiRoot = window.location.origin;
  const domain = apiRoot.split("//")[1];
  const system = lambda.split("_")[0];
  const path = lambda.split("_")[1];

  let lambdaEndpoint;

  if (process.env.NODE_ENV === "development") {
    const portNumber = process.env.REACT_APP_RUNTIME_MODE === "standalone"
      ? process.env.REACT_APP_PORT
      : process.env.REACT_APP_LAMBDA_PORT_NBR;

    lambdaEndpoint = `http://localhost:8080/${path}`;
  } else if (apiRoot.indexOf("automation-eu") > -1) {
    lambdaEndpoint = appTld
      ? `https://${system}.system.automation-eu.floqast.app/${path}`
      : `https://${system}.automation-eu.floqast.engineering/${path}`;
  } else if (apiRoot.indexOf("automation") > -1) {
    lambdaEndpoint = appTld
      ? `https://${system}.system.automation.floqast.app/${path}`
      : `https://${system}.automation.floqast.engineering/${path}`;
  } else if (apiRoot.indexOf("eu.floqast.app") > -1) {
    lambdaEndpoint = `https://${system}.system.eu.floqast.app/${path}`;
  } else if (apiRoot.indexOf("floqast.app") > -1) {
    lambdaEndpoint = `https://${system}.system.floqast.app/${path}`;
  } else {
    lambdaEndpoint = `https://${system}.${domain}/${path}`;
  }

  return lambdaEndpoint;
}

// TODO: temp(?) shim for adding headers to fetch for SDK

const fetchOptions = (options) => {
  const headers = {
    // Authorization: `Bearer ${authState.get("jwt")}`,
    ...options.headers,
  };

  const customOptions = {
    ...options,
    credentials: "include",
    headers,
  };

  return customOptions;
};

export const fetchStuff = (url, options) =>
  window.fetch(url, fetchOptions(options));
